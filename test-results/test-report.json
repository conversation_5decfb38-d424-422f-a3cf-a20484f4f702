{"timestamp": "2025-07-18T12:14:10.418Z", "summary": {"totalTests": 42, "passedTests": 42, "failedTests": 0, "skippedTests": 0, "successRate": 100}, "testSuites": [{"name": "基础测试", "file": "test/basic.test.ts", "tests": 5, "passed": 5, "failed": 0, "duration": "16ms", "details": ["✅ 应该能够运行基本测试", "✅ 应该能够测试字符串", "✅ 应该能够测试对象", "✅ 应该能够测试数组", "✅ 应该能够测试异步函数"]}, {"name": "数据工厂测试", "file": "test/factories/factory.test.ts", "tests": 26, "passed": 26, "failed": 0, "duration": "77ms", "details": ["✅ UserFactory - 用户数据生成 (5个测试)", "✅ ProductFactory - 商品数据生成 (3个测试)", "✅ OrderFactory - 订单数据生成 (4个测试)", "✅ MessageFactory - 消息数据生成 (3个测试)", "✅ ReviewFactory - 评价数据生成 (3个测试)", "✅ FeedbackFactory - 反馈数据生成 (3个测试)", "✅ DataGenerator - 综合数据生成 (2个测试)", "✅ 数据一致性测试 (3个测试)"]}, {"name": "性能测试", "file": "test/performance/simple-performance.test.ts", "tests": 11, "passed": 11, "failed": 0, "duration": "1726ms", "details": ["✅ 数据生成性能测试 (3个测试)", "✅ 并发操作模拟 (2个测试)", "✅ 内存使用测试 (1个测试)", "✅ 算法性能测试 (3个测试)", "✅ 缓存性能模拟 (2个测试)"]}], "performanceMetrics": {"dataGeneration": {"1000个用户": "138.14ms", "500个商品": "40.73ms", "200个订单": "32.95ms"}, "concurrency": {"50个并发查询": "20.82ms", "100个API调用": "0.22ms"}, "algorithms": {"搜索10000个商品": "8.89ms", "排序5000个商品": "3.87ms", "分页处理": "0.00ms"}, "memory": {"2500个对象内存增加": "8.97MB"}, "cache": {"100次缓存查询": "0.05ms", "10次缓存未命中": "0.34ms"}}, "coverage": {"statements": "N/A", "branches": "N/A", "functions": "N/A", "lines": "N/A", "note": "覆盖率报告需要运行 npm run test:coverage"}, "environment": {"nodeVersion": "v22.17.0", "platform": "win32", "arch": "x64", "testFramework": "Vitest 3.2.4", "testEnvironment": "Node.js"}}