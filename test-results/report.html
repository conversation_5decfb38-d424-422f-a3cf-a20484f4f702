
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BitMarket 测试报告</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f5f5f5; padding: 20px; border-radius: 5px; }
        .section { margin: 20px 0; }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
        table { width: 100%; border-collapse: collapse; }
        th, td { padding: 10px; border: 1px solid #ddd; text-align: left; }
        th { background-color: #f8f9fa; }
    </style>
</head>
<body>
    <div class="header">
        <h1>BitMarket 测试报告</h1>
        <p>生成时间: 2025-07-20T05:54:59.650Z</p>
        <p>Node.js 版本: v22.17.0</p>
        <p>平台: win32 (x64)</p>
    </div>
    
    <div class="section">
        <h2>测试结果概览</h2>
        <p>详细的测试结果请查看 test-results/results.json 文件</p>
    </div>
    
    <div class="section">
        <h2>覆盖率报告</h2>
        <p>覆盖率详情请查看 coverage/index.html 文件</p>
    </div>
</body>
</html>
  