{"timestamp": 1752886660976, "summary": {"fixes": 12, "errors": 0, "details": {"fixes": [{"file": "app/admin/announcements/create/page.tsx", "type": "jsx", "description": "修复input标签自闭合"}, {"file": "app\\admin\\announcements\\create\\page.tsx", "type": "null-reference", "description": "添加可选链操作符"}, {"file": "app\\admin\\announcements\\page.tsx", "type": "null-reference", "description": "添加可选链操作符"}, {"file": "app\\admin\\announcements\\[id]\\edit\\page.tsx", "type": "null-reference", "description": "添加可选链操作符"}, {"file": "app\\admin\\disputes\\page.tsx", "type": "null-reference", "description": "添加可选链操作符"}, {"file": "app\\admin\\escrow\\page.tsx", "type": "null-reference", "description": "添加可选链操作符"}, {"file": "app\\admin\\help\\create\\page.tsx", "type": "null-reference", "description": "添加可选链操作符"}, {"file": "app\\admin\\help\\media\\page.tsx", "type": "null-reference", "description": "添加可选链操作符"}, {"file": "app\\admin\\help\\page.tsx", "type": "null-reference", "description": "添加可选链操作符"}, {"file": "app\\admin\\announcements\\[id]\\edit\\page.tsx", "type": "promise", "description": "添加错误处理提醒"}, {"file": "app\\admin\\announcements\\[id]\\page.tsx", "type": "promise", "description": "添加错误处理提醒"}, {"file": "components/templates/SafeComponent.tsx", "type": "template", "description": "创建安全的React组件模板"}], "errors": []}}, "details": {"fixes": [{"file": "app/admin/announcements/create/page.tsx", "type": "jsx", "description": "修复input标签自闭合"}, {"file": "app\\admin\\announcements\\create\\page.tsx", "type": "null-reference", "description": "添加可选链操作符"}, {"file": "app\\admin\\announcements\\page.tsx", "type": "null-reference", "description": "添加可选链操作符"}, {"file": "app\\admin\\announcements\\[id]\\edit\\page.tsx", "type": "null-reference", "description": "添加可选链操作符"}, {"file": "app\\admin\\disputes\\page.tsx", "type": "null-reference", "description": "添加可选链操作符"}, {"file": "app\\admin\\escrow\\page.tsx", "type": "null-reference", "description": "添加可选链操作符"}, {"file": "app\\admin\\help\\create\\page.tsx", "type": "null-reference", "description": "添加可选链操作符"}, {"file": "app\\admin\\help\\media\\page.tsx", "type": "null-reference", "description": "添加可选链操作符"}, {"file": "app\\admin\\help\\page.tsx", "type": "null-reference", "description": "添加可选链操作符"}, {"file": "app\\admin\\announcements\\[id]\\edit\\page.tsx", "type": "promise", "description": "添加错误处理提醒"}, {"file": "app\\admin\\announcements\\[id]\\page.tsx", "type": "promise", "description": "添加错误处理提醒"}, {"file": "components/templates/SafeComponent.tsx", "type": "template", "description": "创建安全的React组件模板"}], "errors": []}}