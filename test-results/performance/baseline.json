{"基线测试_综合": {"testName": "基线测试_综合", "metrics": [{"name": "基线测试_综合_iteration_1", "duration": 0.1764579999999114, "memoryUsage": {"before": {"rss": 954548224, "heapTotal": 211927040, "heapUsed": 114042376, "external": 5562542, "arrayBuffers": 618923}, "after": {"rss": 954548224, "heapTotal": 211927040, "heapUsed": 114407328, "external": 5562542, "arrayBuffers": 618923}, "delta": 364952}, "timestamp": 1753702112336, "metadata": {"iteration": 1}}, {"name": "基线测试_综合_iteration_2", "duration": 0.15904199999999946, "memoryUsage": {"before": {"rss": 954548224, "heapTotal": 211927040, "heapUsed": 114410720, "external": 5562542, "arrayBuffers": 618923}, "after": {"rss": 954564608, "heapTotal": 211927040, "heapUsed": 114776320, "external": 5562542, "arrayBuffers": 618923}, "delta": 365600}, "timestamp": 1753702112336, "metadata": {"iteration": 2}}], "summary": {"avgDuration": 0.16774999999995543, "minDuration": 0.15904199999999946, "maxDuration": 0.1764579999999114, "totalMemoryDelta": 730552, "iterations": 2}}}