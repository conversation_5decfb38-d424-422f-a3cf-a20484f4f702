{"startTime": 1752847095380, "tests": {"database": {"simpleQuery": {"iterations": 10, "successCount": 10, "failureCount": 0, "avgTime": 32.55413999999999, "minTime": 0.8464999999999918, "maxTime": 313.5352, "times": [313.5352, 0.9738999999999578, 0.9520999999999731, 0.8464999999999918, 1.0417999999999665, 3.361599999999953, 1.0009000000000015, 0.9504000000000588, 1.2456000000000813, 1.6333999999999378]}, "complexQuery": {"iterations": 10, "successCount": 10, "failureCount": 0, "avgTime": 5.199000000000012, "minTime": 1.5825999999999567, "maxTime": 17.783900000000017, "times": [17.783900000000017, 4.652399999999943, 2.2291000000000167, 2.094400000000064, 5.943899999999985, 2.6375000000000455, 6.38760000000002, 6.560200000000009, 2.118400000000065, 1.5825999999999567]}, "aggregateQuery": {"iterations": 10, "successCount": 10, "failureCount": 0, "avgTime": 2.2171100000000137, "minTime": 0.50100000000009, "maxTime": 5.523400000000038, "times": [1.6083999999999605, 3.144200000000069, 5.523400000000038, 4.4849999999999, 0.8584000000000742, 0.5818000000000438, 0.5888999999999669, 0.50100000000009, 1.13250000000005, 3.7474999999999454]}, "insertTest": {"iterations": 10, "successCount": 10, "failureCount": 0, "avgTime": 115.21313999999998, "minTime": 49.98819999999978, "maxTime": 411.46539999999993, "times": [411.46539999999993, 212.07870000000003, 60.41470000000004, 61.14480000000003, 90.52610000000004, 71.11959999999999, 57.553100000000086, 49.98819999999978, 63.59270000000015, 74.2480999999998]}}, "memory": {"largeObjectCreation": {"time": 257.06129999999985, "memoryBefore": {"rss": 57.109375, "heapTotal": 12.0625, "heapUsed": 7.297538757324219, "external": 1.9144172668457031}, "memoryAfter": {"rss": 155.99609375, "heapTotal": 130.87109375, "heapUsed": 97.11473083496094, "external": 1.9144353866577148}, "memoryAfterCleanup": {"rss": 153.37109375, "heapTotal": 40.8125, "heapUsed": 6.643157958984375, "external": 1.9143972396850586}, "memoryGrowth": 89.81719207763672, "memoryRecovered": 90.47157287597656}, "stringConcatenation": {"time": 28.308300000000145, "memoryBefore": {"rss": 153.37109375, "heapTotal": 40.8125, "heapUsed": 6.658668518066406, "external": 1.9143972396850586}, "memoryAfter": {"rss": 157.34765625, "heapTotal": 40.8125, "heapUsed": 12.386421203613281, "external": 1.9143972396850586}, "memoryAfterCleanup": {"rss": 157.3671875, "heapTotal": 39.8125, "heapUsed": 6.6480712890625, "external": 1.9143972396850586}, "memoryGrowth": 5.727752685546875, "memoryRecovered": 5.738349914550781}}, "concurrent": {"databaseConcurrency": {"1": {"time": 2.357100000000173, "concurrency": 1, "successful": 1, "failed": 0, "avgTimePerRequest": 2.357100000000173}, "5": {"time": 7.494300000000294, "concurrency": 5, "successful": 5, "failed": 0, "avgTimePerRequest": 1.4988600000000587}, "10": {"time": 14.877200000000357, "concurrency": 10, "successful": 10, "failed": 0, "avgTimePerRequest": 1.4877200000000357}, "20": {"time": 29.200000000000273, "concurrency": 20, "successful": 20, "failed": 0, "avgTimePerRequest": 1.4600000000000137}, "50": {"time": 44.27440000000024, "concurrency": 50, "successful": 50, "failed": 0, "avgTimePerRequest": 0.8854880000000048}}, "memoryAllocation": {"1": {"time": 33.91960000000017, "concurrency": 1, "avgTimePerTask": 33.91960000000017}, "5": {"time": 26.22609999999986, "concurrency": 5, "avgTimePerTask": 5.245219999999972}, "10": {"time": 30.383699999999862, "concurrency": 10, "avgTimePerTask": 3.0383699999999862}, "20": {"time": 26.296299999999974, "concurrency": 20, "avgTimePerTask": 1.3148149999999987}, "50": {"time": 33.45589999999993, "concurrency": 50, "avgTimePerTask": 0.6691179999999985}}}}, "summary": {"totalTime": 3766, "endTime": 1752847099146, "rating": {"score": 90, "grade": "A+", "description": "性能优秀，系统运行非常稳定", "issues": ["insertTest 响应时间过长 (115.21ms)"]}}}