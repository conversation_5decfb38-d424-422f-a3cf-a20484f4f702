{"timestamp": 1752884688275, "summary": {"staticPages": 0, "isrPages": 2, "dynamicPages": 4, "totalPages": 6, "optimizationRatio": "33.3"}, "pages": {"static": [], "isr": [{"path": "/", "file": "app/page.tsx", "name": "首页", "hasAuth": true, "hasState": true, "hasEffect": true, "hasForm": false, "hasApi": true, "hasParams": false, "isClient": true, "hasRealTime": false}, {"path": "/admin", "file": "app/admin/page.tsx", "name": "管理后台", "hasAuth": true, "hasState": true, "hasEffect": true, "hasForm": false, "hasApi": true, "hasParams": false, "isClient": true, "hasRealTime": false}], "dynamic": [{"path": "/help", "file": "app/help/page.tsx", "name": "帮助页面", "hasAuth": true, "hasState": true, "hasEffect": true, "hasForm": false, "hasApi": true, "hasParams": true, "isClient": true, "hasRealTime": false}, {"path": "/settings", "file": "app/settings/page.tsx", "name": "设置页面", "hasAuth": true, "hasState": true, "hasEffect": true, "hasForm": true, "hasApi": true, "hasParams": true, "isClient": true, "hasRealTime": false}, {"path": "/profile", "file": "app/profile/page.tsx", "name": "用户资料", "hasAuth": true, "hasState": true, "hasEffect": true, "hasForm": true, "hasApi": true, "hasParams": false, "isClient": true, "hasRealTime": false}, {"path": "/auth/signin", "file": "app/auth/signin/page.tsx", "name": "登录页面", "hasAuth": false, "hasState": true, "hasEffect": false, "hasForm": true, "hasApi": true, "hasParams": false, "isClient": true, "hasRealTime": false}]}}