{"timestamp": "2025-07-18T12:33:54.028Z", "summary": {"dataGenerationRate": 426718, "cachePerformance": 4151789, "databaseAvgTime": 23.582775000000026, "concurrencyRate": 1657825, "memoryEfficiency": 1625.6304}, "details": {"dataGeneration": {"users": {"count": 10000, "time": 312.27889999999996, "rate": 32022.656670047196}, "products": {"count": 5000, "time": 15.365299999999934, "rate": 325408.5504350726}, "orders": {"count": 2000, "time": 2.1674999999999045, "rate": 922722.0299885067}}, "caching": {"set": {"operations": 1000, "time": 0.6545999999999594, "opsPerSecond": 1527650.4735717415}, "get": {"operations": 5000, "time": 1.2042999999999893, "opsPerSecond": 4151789.4212405914}, "multiLevel": {"operations": 1000, "hits": 1000, "hitRate": 100, "time": 0.4436999999999216, "opsPerSecond": 2253775.073248088}}, "database": {"queries": 4, "totalTime": 94.43419999999992, "avgTime": 23.582775000000026, "indexHitRate": 80.44992060137402}, "concurrency": {"operations": 1000, "time": 0.6032000000000153, "opsPerSecond": 1657824.9336869605, "successRate": 100}, "memory": {"objects": 10000, "memoryUsed": 16256304, "memoryPerObject": 1625.6304, "heapUsed": 40849400, "heapTotal": 68038656, "usage": 60.03851692778881}}, "goals": {"dataGeneration": "❌ 未达标", "caching": "✅ 达标", "database": "✅ 达标", "concurrency": "✅ 达标", "memory": "✅ 达标"}}