{"optimizations": 15, "issues": 1, "performance": {"score": 81, "grade": "A+", "description": "优化效果显著", "expectedImprovement": "50-70%编译时间减少"}, "timestamp": 1752884070804, "optimizationDetails": ["✅ 已移除过时的 swcMinify 配置", "✅ 已移除不兼容的 incrementalCacheHandlerPath 配置", "✅ 已优化文件监听延迟", "✅ 增量编译已启用", "✅ 跳过库检查已启用", "✅ 跳过默认库检查已启用", "✅ 模块解析优化已启用", "✅ 模块语法优化已启用", "✅ 已创建优化组件: OptimizedIcon.tsx", "✅ 已创建优化组件: DynamicComponent.tsx", "✅ 已移除过时的缓存处理器", "📦 已识别大型依赖: 图标库 (@heroicons/react)", "📦 已识别大型依赖: WebSocket库 (socket.io)", "📦 已识别大型依赖: 数据库客户端 (@prisma/client)", "📦 已识别大型依赖: CSS框架 (tailwindcss)"], "issueDetails": ["⚠️  依赖数量较多，考虑清理未使用的依赖"]}