
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BitMarket 性能分析报告</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 8px; margin-bottom: 20px; }
        .card { background: white; border-radius: 8px; padding: 20px; margin-bottom: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .metric { display: inline-block; margin: 10px; padding: 15px; background: #f8f9fa; border-radius: 5px; min-width: 150px; text-align: center; }
        .regression { background: #f8d7da; color: #721c24; }
        .improvement { background: #d4edda; color: #155724; }
        .chart-container { width: 100%; height: 400px; margin: 20px 0; }
        table { width: 100%; border-collapse: collapse; }
        th, td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background-color: #f8f9fa; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>⚡ BitMarket 性能分析报告</h1>
            <p>生成时间: 2025-07-28T11:28:32.337Z</p>
        </div>
        
        <div class="card">
            <h2>📊 总览</h2>
            <div class="metric">
                <h3>1</h3>
                <p>基准测试</p>
            </div>
            <div class="metric">
                <h3>2</h3>
                <p>性能指标</p>
            </div>
        </div>
        
        <div class="card">
            <h2>🔍 性能回归检测</h2>
            <table>
                <thead>
                    <tr>
                        <th>测试名称</th>
                        <th>当前性能 (ms)</th>
                        <th>基线性能 (ms)</th>
                        <th>变化率</th>
                        <th>状态</th>
                    </tr>
                </thead>
                <tbody>
                    
                        <tr class="improvement">
                            <td>基线测试_综合</td>
                            <td>0.17</td>
                            <td>0.38</td>
                            <td>-55.5%</td>
                            <td>🟢 改进</td>
                        </tr>
                    
                </tbody>
            </table>
        </div>
        
        <div class="card">
            <h2>📈 基准测试结果</h2>
            
                <h3>基线测试_综合</h3>
                <div class="metric">
                    <h4>0.17ms</h4>
                    <p>平均耗时</p>
                </div>
                <div class="metric">
                    <h4>0.16ms</h4>
                    <p>最小耗时</p>
                </div>
                <div class="metric">
                    <h4>0.18ms</h4>
                    <p>最大耗时</p>
                </div>
                <div class="metric">
                    <h4>0.70MB</h4>
                    <p>内存使用</p>
                </div>
            
        </div>
    </div>
</body>
</html>
    