{"numTotalTestSuites": 70, "numPassedTestSuites": 55, "numFailedTestSuites": 15, "numPendingTestSuites": 0, "numTotalTests": 113, "numPassedTests": 107, "numFailedTests": 6, "numPendingTests": 0, "numTodoTests": 0, "snapshot": {"added": 0, "failure": false, "filesAdded": 0, "filesRemoved": 0, "filesRemovedList": [], "filesUnmatched": 0, "filesUpdated": 0, "matched": 0, "total": 0, "unchecked": 0, "uncheckedKeysByFile": [], "unmatched": 0, "updated": 0, "didUpdate": false}, "startTime": 1753702110743, "success": false, "testResults": [{"assertionResults": [{"ancestorTitles": ["基础测试"], "fullName": "基础测试 应该能够运行基本测试", "status": "passed", "title": "应该能够运行基本测试", "duration": 0.5265830000003007, "failureMessages": [], "location": {"line": 4, "column": 3}, "meta": {}}, {"ancestorTitles": ["基础测试"], "fullName": "基础测试 应该能够测试字符串", "status": "passed", "title": "应该能够测试字符串", "duration": 0.0782920000001468, "failureMessages": [], "location": {"line": 8, "column": 3}, "meta": {}}, {"ancestorTitles": ["基础测试"], "fullName": "基础测试 应该能够测试对象", "status": "passed", "title": "应该能够测试对象", "duration": 0.22754200000008495, "failureMessages": [], "location": {"line": 12, "column": 3}, "meta": {}}, {"ancestorTitles": ["基础测试"], "fullName": "基础测试 应该能够测试数组", "status": "passed", "title": "应该能够测试数组", "duration": 0.3964169999999285, "failureMessages": [], "location": {"line": 17, "column": 3}, "meta": {}}, {"ancestorTitles": ["基础测试"], "fullName": "基础测试 应该能够测试异步函数", "status": "passed", "title": "应该能够测试异步函数", "duration": 0.18641699999989214, "failureMessages": [], "location": {"line": 23, "column": 3}, "meta": {}}], "startTime": 1753702113347, "endTime": 1753702113348.3965, "status": "passed", "message": "", "name": "/Users/<USER>/Downloads/bitmarket-v1.3.0-Re-Made/test/basic.test.ts"}, {"assertionResults": [], "startTime": 1753702110743, "endTime": 1753702110743, "status": "failed", "message": "[vitest] There was an error when mocking a module. If you are using \"vi.mock\" factory, make sure there are no top level variables inside, since this call is hoisted to top of the file. Read more: https://vitest.dev/api/vi.html#vi-mock", "name": "/Users/<USER>/Downloads/bitmarket-v1.3.0-Re-Made/test/escrow-service.test.ts"}, {"assertionResults": [{"ancestorTitles": ["FavoriteButton"], "fullName": "FavoriteButton should not render when user is not logged in", "status": "passed", "title": "should not render when user is not logged in", "duration": 5.569833999999901, "failureMessages": [], "location": {"line": 25, "column": 3}, "meta": {}}, {"ancestorTitles": ["FavoriteButton"], "fullName": "FavoriteButton should render heart icon when user is logged in", "status": "passed", "title": "should render heart icon when user is logged in", "duration": 28.00387499999988, "failureMessages": [], "location": {"line": 36, "column": 3}, "meta": {}}, {"ancestorTitles": ["FavoriteButton"], "fullName": "FavoriteButton should show solid heart when product is favorited", "status": "passed", "title": "should show solid heart when product is favorited", "duration": 6.9780000000000655, "failureMessages": [], "location": {"line": 59, "column": 3}, "meta": {}}, {"ancestorTitles": ["FavoriteButton"], "fullName": "FavoriteButton should toggle favorite status when clicked", "status": "passed", "title": "should toggle favorite status when clicked", "duration": 6.58529099999987, "failureMessages": [], "location": {"line": 83, "column": 3}, "meta": {}}, {"ancestorTitles": ["FavoriteButton"], "fullName": "Favorite<PERSON><PERSON><PERSON> should show text when showText prop is true", "status": "passed", "title": "should show text when showText prop is true", "duration": 4.149417000000085, "failureMessages": [], "location": {"line": 127, "column": 3}, "meta": {}}], "startTime": 1753702112783, "endTime": 1753702112834.1494, "status": "passed", "message": "", "name": "/Users/<USER>/Downloads/bitmarket-v1.3.0-Re-Made/app/__tests__/favorites.test.tsx"}, {"assertionResults": [], "startTime": 1753702110743, "endTime": 1753702110743, "status": "failed", "message": "[vitest] There was an error when mocking a module. If you are using \"vi.mock\" factory, make sure there are no top level variables inside, since this call is hoisted to top of the file. Read more: https://vitest.dev/api/vi.html#vi-mock", "name": "/Users/<USER>/Downloads/bitmarket-v1.3.0-Re-Made/test/e2e/admin-functions.test.ts"}, {"assertionResults": [], "startTime": 1753702110743, "endTime": 1753702110743, "status": "failed", "message": "[vitest] There was an error when mocking a module. If you are using \"vi.mock\" factory, make sure there are no top level variables inside, since this call is hoisted to top of the file. Read more: https://vitest.dev/api/vi.html#vi-mock", "name": "/Users/<USER>/Downloads/bitmarket-v1.3.0-Re-Made/test/e2e/business-flow.test.ts"}, {"assertionResults": [], "startTime": 1753702110743, "endTime": 1753702110743, "status": "failed", "message": "[vitest] There was an error when mocking a module. If you are using \"vi.mock\" factory, make sure there are no top level variables inside, since this call is hoisted to top of the file. Read more: https://vitest.dev/api/vi.html#vi-mock", "name": "/Users/<USER>/Downloads/bitmarket-v1.3.0-Re-Made/test/e2e/user-flow.test.ts"}, {"assertionResults": [{"ancestorTitles": ["认证API测试", "POST /api/auth/register - 用户注册"], "fullName": "认证API测试 POST /api/auth/register - 用户注册 应该成功注册新用户", "status": "failed", "title": "应该成功注册新用户", "duration": 8.078333000000157, "failureMessages": ["AssertionError: expected 400 to be 200 // Object.is equality\n    at /Users/<USER>/Downloads/bitmarket-v1.3.0-Re-Made/test/api/auth.test.ts:78:31\n    at file:///Users/<USER>/Downloads/bitmarket-v1.3.0-Re-Made/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected 400 to be 200 // Object.is equality\n    at /Users/<USER>/Downloads/bitmarket-v1.3.0-Re-Made/test/api/auth.test.ts:78:31\n    at file:///Users/<USER>/Downloads/bitmarket-v1.3.0-Re-Made/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected 400 to be 200 // Object.is equality\n    at /Users/<USER>/Downloads/bitmarket-v1.3.0-Re-Made/test/api/auth.test.ts:78:31\n    at file:///Users/<USER>/Downloads/bitmarket-v1.3.0-Re-Made/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 52, "column": 5}, "meta": {}}, {"ancestorTitles": ["认证API测试", "POST /api/auth/register - 用户注册"], "fullName": "认证API测试 POST /api/auth/register - 用户注册 应该拒绝重复邮箱注册", "status": "passed", "title": "应该拒绝重复邮箱注册", "duration": 1.393333999999868, "failureMessages": [], "location": {"line": 103, "column": 5}, "meta": {}}, {"ancestorTitles": ["认证API测试", "POST /api/auth/register - 用户注册"], "fullName": "认证API测试 POST /api/auth/register - 用户注册 应该验证必填字段", "status": "passed", "title": "应该验证必填字段", "duration": 0.7170000000000982, "failureMessages": [], "location": {"line": 121, "column": 5}, "meta": {}}, {"ancestorTitles": ["认证API测试", "POST /api/auth/register - 用户注册"], "fullName": "认证API测试 POST /api/auth/register - 用户注册 应该处理数据库错误", "status": "passed", "title": "应该处理数据库错误", "duration": 1.2147919999999885, "failureMessages": [], "location": {"line": 136, "column": 5}, "meta": {}}, {"ancestorTitles": ["认证API测试", "POST /api/auth/socket-token - Socket认证令牌"], "fullName": "认证API测试 POST /api/auth/socket-token - Socket认证令牌 应该为已登录用户生成Socket令牌", "status": "failed", "title": "应该为已登录用户生成Socket令牌", "duration": 10.164749999999913, "failureMessages": ["AssertionError: expected 500 to be 200 // Object.is equality\n    at /Users/<USER>/Downloads/bitmarket-v1.3.0-Re-Made/test/api/auth.test.ts:172:31\n    at file:///Users/<USER>/Downloads/bitmarket-v1.3.0-Re-Made/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected 500 to be 200 // Object.is equality\n    at /Users/<USER>/Downloads/bitmarket-v1.3.0-Re-Made/test/api/auth.test.ts:172:31\n    at file:///Users/<USER>/Downloads/bitmarket-v1.3.0-Re-Made/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected 500 to be 200 // Object.is equality\n    at /Users/<USER>/Downloads/bitmarket-v1.3.0-Re-Made/test/api/auth.test.ts:172:31\n    at file:///Users/<USER>/Downloads/bitmarket-v1.3.0-Re-Made/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 156, "column": 5}, "meta": {}}, {"ancestorTitles": ["认证API测试", "POST /api/auth/socket-token - Socket认证令牌"], "fullName": "认证API测试 POST /api/auth/socket-token - Socket认证令牌 应该拒绝未登录用户", "status": "failed", "title": "应该拒绝未登录用户", "duration": 5.213958000000048, "failureMessages": ["AssertionError: expected 500 to be 401 // Object.is equality\n    at /Users/<USER>/Downloads/bitmarket-v1.3.0-Re-Made/test/api/auth.test.ts:195:31\n    at file:///Users/<USER>/Downloads/bitmarket-v1.3.0-Re-Made/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected 500 to be 401 // Object.is equality\n    at /Users/<USER>/Downloads/bitmarket-v1.3.0-Re-Made/test/api/auth.test.ts:195:31\n    at file:///Users/<USER>/Downloads/bitmarket-v1.3.0-Re-Made/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected 500 to be 401 // Object.is equality\n    at /Users/<USER>/Downloads/bitmarket-v1.3.0-Re-Made/test/api/auth.test.ts:195:31\n    at file:///Users/<USER>/Downloads/bitmarket-v1.3.0-Re-Made/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 189, "column": 5}, "meta": {}}, {"ancestorTitles": ["认证API测试", "POST /api/auth/socket-token - Socket认证令牌"], "fullName": "认证API测试 POST /api/auth/socket-token - Socket认证令牌 应该处理JWT签名错误", "status": "passed", "title": "应该处理JWT签名错误", "duration": 0.7011670000001686, "failureMessages": [], "location": {"line": 201, "column": 5}, "meta": {}}, {"ancestorTitles": ["认证API测试", "认证中间件测试"], "fullName": "认证API测试 认证中间件测试 应该验证有效的JWT令牌", "status": "passed", "title": "应该验证有效的JWT令牌", "duration": 0.31920800000011695, "failureMessages": [], "location": {"line": 227, "column": 5}, "meta": {}}, {"ancestorTitles": ["认证API测试", "认证中间件测试"], "fullName": "认证API测试 认证中间件测试 应该拒绝无效的JWT令牌", "status": "passed", "title": "应该拒绝无效的JWT令牌", "duration": 0.3461250000000291, "failureMessages": [], "location": {"line": 243, "column": 5}, "meta": {}}], "startTime": 1753702111690, "endTime": 1753702111718.3462, "status": "failed", "message": "", "name": "/Users/<USER>/Downloads/bitmarket-v1.3.0-Re-Made/test/api/auth.test.ts"}, {"assertionResults": [{"ancestorTitles": ["消息API测试", "GET /api/messages - 获取消息列表"], "fullName": "消息API测试 GET /api/messages - 获取消息列表 应该返回订单相关的消息", "status": "passed", "title": "应该返回订单相关的消息", "duration": 4.771959000000152, "failureMessages": [], "location": {"line": 45, "column": 5}, "meta": {}}, {"ancestorTitles": ["消息API测试", "GET /api/messages - 获取消息列表"], "fullName": "消息API测试 GET /api/messages - 获取消息列表 应该拒绝无关用户访问消息", "status": "passed", "title": "应该拒绝无关用户访问消息", "duration": 0.9250000000001819, "failureMessages": [], "location": {"line": 107, "column": 5}, "meta": {}}, {"ancestorTitles": ["消息API测试", "GET /api/messages - 获取消息列表"], "fullName": "消息API测试 GET /api/messages - 获取消息列表 应该处理订单不存在的情况", "status": "passed", "title": "应该处理订单不存在的情况", "duration": 1.1832079999999223, "failureMessages": [], "location": {"line": 128, "column": 5}, "meta": {}}, {"ancestorTitles": ["消息API测试", "POST /api/messages - 发送消息"], "fullName": "消息API测试 POST /api/messages - 发送消息 应该成功发送文本消息", "status": "passed", "title": "应该成功发送文本消息", "duration": 0.793999999999869, "failureMessages": [], "location": {"line": 145, "column": 5}, "meta": {}}, {"ancestorTitles": ["消息API测试", "POST /api/messages - 发送消息"], "fullName": "消息API测试 POST /api/messages - 发送消息 应该成功发送图片消息", "status": "passed", "title": "应该成功发送图片消息", "duration": 0.5679580000000897, "failureMessages": [], "location": {"line": 217, "column": 5}, "meta": {}}, {"ancestorTitles": ["消息API测试", "POST /api/messages - 发送消息"], "fullName": "消息API测试 POST /api/messages - 发送消息 应该验证文本消息的内容", "status": "passed", "title": "应该验证文本消息的内容", "duration": 0.4333329999999478, "failureMessages": [], "location": {"line": 267, "column": 5}, "meta": {}}, {"ancestorTitles": ["消息API测试", "POST /api/messages - 发送消息"], "fullName": "消息API测试 POST /api/messages - 发送消息 应该验证文件消息的文件URL", "status": "passed", "title": "应该验证文件消息的文件URL", "duration": 0.3352909999998701, "failureMessages": [], "location": {"line": 286, "column": 5}, "meta": {}}, {"ancestorTitles": ["消息API测试", "POST /api/messages - 发送消息"], "fullName": "消息API测试 POST /api/messages - 发送消息 应该拒绝未登录用户发送消息", "status": "passed", "title": "应该拒绝未登录用户发送消息", "duration": 0.277625000000171, "failureMessages": [], "location": {"line": 306, "column": 5}, "meta": {}}, {"ancestorTitles": ["消息API测试", "POST /api/messages - 发送消息"], "fullName": "消息API测试 POST /api/messages - 发送消息 应该拒绝无关用户发送消息", "status": "passed", "title": "应该拒绝无关用户发送消息", "duration": 0.32195899999987887, "failureMessages": [], "location": {"line": 324, "column": 5}, "meta": {}}, {"ancestorTitles": ["消息API测试", "POST /api/messages - 发送消息"], "fullName": "消息API测试 POST /api/messages - 发送消息 应该处理数据库错误", "status": "passed", "title": "应该处理数据库错误", "duration": 0.9708749999999782, "failureMessages": [], "location": {"line": 351, "column": 5}, "meta": {}}], "startTime": 1753702113109, "endTime": 1753702113119.971, "status": "passed", "message": "", "name": "/Users/<USER>/Downloads/bitmarket-v1.3.0-Re-Made/test/api/messages.test.ts"}, {"assertionResults": [{"ancestorTitles": ["订单API测试", "POST /api/orders - 创建订单"], "fullName": "订单API测试 POST /api/orders - 创建订单 应该成功创建订单", "status": "passed", "title": "应该成功创建订单", "duration": 4.776749999999993, "failureMessages": [], "location": {"line": 89, "column": 5}, "meta": {}}, {"ancestorTitles": ["订单API测试", "POST /api/orders - 创建订单"], "fullName": "订单API测试 POST /api/orders - 创建订单 应该拒绝购买自己的商品", "status": "passed", "title": "应该拒绝购买自己的商品", "duration": 0.8273330000001806, "failureMessages": [], "location": {"line": 161, "column": 5}, "meta": {}}, {"ancestorTitles": ["订单API测试", "POST /api/orders - 创建订单"], "fullName": "订单API测试 POST /api/orders - 创建订单 应该检查库存不足", "status": "passed", "title": "应该检查库存不足", "duration": 1.1808750000000146, "failureMessages": [], "location": {"line": 195, "column": 5}, "meta": {}}, {"ancestorTitles": ["订单API测试", "GET /api/orders/[id] - 获取订单详情"], "fullName": "订单API测试 GET /api/orders/[id] - 获取订单详情 应该返回买家的订单详情", "status": "passed", "title": "应该返回买家的订单详情", "duration": 0.8172499999996035, "failureMessages": [], "location": {"line": 232, "column": 5}, "meta": {}}, {"ancestorTitles": ["订单API测试", "GET /api/orders/[id] - 获取订单详情"], "fullName": "订单API测试 GET /api/orders/[id] - 获取订单详情 应该拒绝无关用户访问订单", "status": "passed", "title": "应该拒绝无关用户访问订单", "duration": 0.5051249999996799, "failureMessages": [], "location": {"line": 261, "column": 5}, "meta": {}}, {"ancestorTitles": ["订单API测试", "PATCH /api/orders/[id] - 更新订单状态"], "fullName": "订单API测试 PATCH /api/orders/[id] - 更新订单状态 应该允许买家上传支付凭证", "status": "passed", "title": "应该允许买家上传支付凭证", "duration": 2.2159590000001117, "failureMessages": [], "location": {"line": 287, "column": 5}, "meta": {}}, {"ancestorTitles": ["订单API测试", "PATCH /api/orders/[id] - 更新订单状态"], "fullName": "订单API测试 PATCH /api/orders/[id] - 更新订单状态 应该允许卖家确认支付", "status": "passed", "title": "应该允许卖家确认支付", "duration": 0.7824169999998958, "failureMessages": [], "location": {"line": 326, "column": 5}, "meta": {}}, {"ancestorTitles": ["订单API测试", "POST /api/orders/[id]/payment - 处理支付"], "fullName": "订单API测试 POST /api/orders/[id]/payment - 处理支付 应该处理币安支付", "status": "passed", "title": "应该处理币安支付", "duration": 0.8223750000001928, "failureMessages": [], "location": {"line": 363, "column": 5}, "meta": {}}, {"ancestorTitles": ["订单API测试", "POST /api/orders/[id]/payment - 处理支付"], "fullName": "订单API测试 POST /api/orders/[id]/payment - 处理支付 应该处理BNB链支付", "status": "passed", "title": "应该处理BNB链支付", "duration": 0.49687500000027285, "failureMessages": [], "location": {"line": 404, "column": 5}, "meta": {}}, {"ancestorTitles": ["订单API测试", "GET /api/orders/[id]/payment - 获取支付信息"], "fullName": "订单API测试 GET /api/orders/[id]/payment - 获取支付信息 应该为买家生成支付二维码", "status": "passed", "title": "应该为买家生成支付二维码", "duration": 0.4695830000000569, "failureMessages": [], "location": {"line": 445, "column": 5}, "meta": {}}], "startTime": 1753702112901, "endTime": 1753702112913.4968, "status": "passed", "message": "", "name": "/Users/<USER>/Downloads/bitmarket-v1.3.0-Re-Made/test/api/orders.test.ts"}, {"assertionResults": [{"ancestorTitles": ["商品API测试", "GET /api/products - 获取商品列表"], "fullName": "商品API测试 GET /api/products - 获取商品列表 应该返回已审核通过的商品列表", "status": "passed", "title": "应该返回已审核通过的商品列表", "duration": 5.370957999999973, "failureMessages": [], "location": {"line": 119, "column": 5}, "meta": {}}, {"ancestorTitles": ["商品API测试", "GET /api/products - 获取商品列表"], "fullName": "商品API测试 GET /api/products - 获取商品列表 应该支持搜索功能", "status": "passed", "title": "应该支持搜索功能", "duration": 1.1514589999997042, "failureMessages": [], "location": {"line": 155, "column": 5}, "meta": {}}, {"ancestorTitles": ["商品API测试", "GET /api/products - 获取商品列表"], "fullName": "商品API测试 GET /api/products - 获取商品列表 应该支持分类筛选", "status": "passed", "title": "应该支持分类筛选", "duration": 0.9297920000003614, "failureMessages": [], "location": {"line": 179, "column": 5}, "meta": {}}, {"ancestorTitles": ["商品API测试", "GET /api/products - 获取商品列表"], "fullName": "商品API测试 GET /api/products - 获取商品列表 应该支持价格范围筛选", "status": "passed", "title": "应该支持价格范围筛选", "duration": 1.3716249999997672, "failureMessages": [], "location": {"line": 203, "column": 5}, "meta": {}}, {"ancestorTitles": ["商品API测试", "POST /api/products - 创建商品"], "fullName": "商品API测试 POST /api/products - 创建商品 应该成功创建商品", "status": "passed", "title": "应该成功创建商品", "duration": 1.0327080000001843, "failureMessages": [], "location": {"line": 231, "column": 5}, "meta": {}}, {"ancestorTitles": ["商品API测试", "POST /api/products - 创建商品"], "fullName": "商品API测试 POST /api/products - 创建商品 应该拒绝未登录用户创建商品", "status": "passed", "title": "应该拒绝未登录用户创建商品", "duration": 0.42454099999986283, "failureMessages": [], "location": {"line": 292, "column": 5}, "meta": {}}, {"ancestorTitles": ["商品API测试", "POST /api/products - 创建商品"], "fullName": "商品API测试 POST /api/products - 创建商品 应该验证必填字段", "status": "passed", "title": "应该验证必填字段", "duration": 0.36904199999980847, "failureMessages": [], "location": {"line": 309, "column": 5}, "meta": {}}, {"ancestorTitles": ["商品API测试", "GET /api/products/[id] - 获取商品详情"], "fullName": "商品API测试 GET /api/products/[id] - 获取商品详情 应该返回商品详情", "status": "passed", "title": "应该返回商品详情", "duration": 0.6087499999998727, "failureMessages": [], "location": {"line": 330, "column": 5}, "meta": {}}, {"ancestorTitles": ["商品API测试", "GET /api/products/[id] - 获取商品详情"], "fullName": "商品API测试 GET /api/products/[id] - 获取商品详情 应该处理商品不存在的情况", "status": "passed", "title": "应该处理商品不存在的情况", "duration": 0.5042499999999563, "failureMessages": [], "location": {"line": 353, "column": 5}, "meta": {}}, {"ancestorTitles": ["商品API测试", "PATCH /api/products/[id] - 更新商品"], "fullName": "商品API测试 PATCH /api/products/[id] - 更新商品 应该允许卖家更新自己的商品", "status": "passed", "title": "应该允许卖家更新自己的商品", "duration": 0.8467920000002778, "failureMessages": [], "location": {"line": 369, "column": 5}, "meta": {}}, {"ancestorTitles": ["商品API测试", "PATCH /api/products/[id] - 更新商品"], "fullName": "商品API测试 PATCH /api/products/[id] - 更新商品 应该拒绝非卖家更新商品", "status": "passed", "title": "应该拒绝非卖家更新商品", "duration": 0.675083000000086, "failureMessages": [], "location": {"line": 429, "column": 5}, "meta": {}}], "startTime": 1753702112824, "endTime": 1753702112837.675, "status": "passed", "message": "", "name": "/Users/<USER>/Downloads/bitmarket-v1.3.0-Re-Made/test/api/products.test.ts"}, {"assertionResults": [], "startTime": 1753702110743, "endTime": 1753702110743, "status": "failed", "message": "[vitest] There was an error when mocking a module. If you are using \"vi.mock\" factory, make sure there are no top level variables inside, since this call is hoisted to top of the file. Read more: https://vitest.dev/api/vi.html#vi-mock", "name": "/Users/<USER>/Downloads/bitmarket-v1.3.0-Re-Made/test/api/simple-api.test.ts"}, {"assertionResults": [], "startTime": 1753702110743, "endTime": 1753702110743, "status": "failed", "message": "[vitest] There was an error when mocking a module. If you are using \"vi.mock\" factory, make sure there are no top level variables inside, since this call is hoisted to top of the file. Read more: https://vitest.dev/api/vi.html#vi-mock", "name": "/Users/<USER>/Downloads/bitmarket-v1.3.0-Re-Made/test/api/upload.test.ts"}, {"assertionResults": [{"ancestorTitles": ["数据工厂测试", "UserFactory"], "fullName": "数据工厂测试 UserFactory 应该创建有效的用户数据", "status": "passed", "title": "应该创建有效的用户数据", "duration": 1.734124999999949, "failureMessages": [], "location": {"line": 14, "column": 5}, "meta": {}}, {"ancestorTitles": ["数据工厂测试", "UserFactory"], "fullName": "数据工厂测试 UserFactory 应该创建买家用户", "status": "passed", "title": "应该创建买家用户", "duration": 0.32058299999971496, "failureMessages": [], "location": {"line": 28, "column": 5}, "meta": {}}, {"ancestorTitles": ["数据工厂测试", "UserFactory"], "fullName": "数据工厂测试 UserFactory 应该创建卖家用户", "status": "passed", "title": "应该创建卖家用户", "duration": 0.22983399999975518, "failureMessages": [], "location": {"line": 35, "column": 5}, "meta": {}}, {"ancestorTitles": ["数据工厂测试", "UserFactory"], "fullName": "数据工厂测试 UserFactory 应该创建管理员用户", "status": "passed", "title": "应该创建管理员用户", "duration": 0.21345800000017334, "failureMessages": [], "location": {"line": 43, "column": 5}, "meta": {}}, {"ancestorTitles": ["数据工厂测试", "UserFactory"], "fullName": "数据工厂测试 UserFactory 应该批量创建用户", "status": "passed", "title": "应该批量创建用户", "duration": 0.8392920000001141, "failureMessages": [], "location": {"line": 51, "column": 5}, "meta": {}}, {"ancestorTitles": ["数据工厂测试", "ProductFactory"], "fullName": "数据工厂测试 ProductFactory 应该创建有效的商品数据", "status": "passed", "title": "应该创建有效的商品数据", "duration": 0.4149589999997261, "failureMessages": [], "location": {"line": 63, "column": 5}, "meta": {}}, {"ancestorTitles": ["数据工厂测试", "ProductFactory"], "fullName": "数据工厂测试 ProductFactory 应该创建可用商品", "status": "passed", "title": "应该创建可用商品", "duration": 0.15733399999999165, "failureMessages": [], "location": {"line": 76, "column": 5}, "meta": {}}, {"ancestorTitles": ["数据工厂测试", "ProductFactory"], "fullName": "数据工厂测试 ProductFactory 应该创建待审核商品", "status": "passed", "title": "应该创建待审核商品", "duration": 0.06704200000012861, "failureMessages": [], "location": {"line": 84, "column": 5}, "meta": {}}, {"ancestorTitles": ["数据工厂测试", "OrderFactory"], "fullName": "数据工厂测试 OrderFactory 应该创建有效的订单数据", "status": "passed", "title": "应该创建有效的订单数据", "duration": 0.37333300000000236, "failureMessages": [], "location": {"line": 92, "column": 5}, "meta": {}}, {"ancestorTitles": ["数据工厂测试", "OrderFactory"], "fullName": "数据工厂测试 OrderFactory 应该创建待支付订单", "status": "passed", "title": "应该创建待支付订单", "duration": 0.1455829999999878, "failureMessages": [], "location": {"line": 105, "column": 5}, "meta": {}}, {"ancestorTitles": ["数据工厂测试", "OrderFactory"], "fullName": "数据工厂测试 OrderFactory 应该创建已支付订单", "status": "passed", "title": "应该创建已支付订单", "duration": 0.12137500000017099, "failureMessages": [], "location": {"line": 114, "column": 5}, "meta": {}}, {"ancestorTitles": ["数据工厂测试", "OrderFactory"], "fullName": "数据工厂测试 OrderFactory 应该创建已完成订单", "status": "passed", "title": "应该创建已完成订单", "duration": 0.1414999999997235, "failureMessages": [], "location": {"line": 124, "column": 5}, "meta": {}}, {"ancestorTitles": ["数据工厂测试", "MessageFactory"], "fullName": "数据工厂测试 MessageFactory 应该创建有效的消息数据", "status": "passed", "title": "应该创建有效的消息数据", "duration": 0.239708999999948, "failureMessages": [], "location": {"line": 136, "column": 5}, "meta": {}}, {"ancestorTitles": ["数据工厂测试", "MessageFactory"], "fullName": "数据工厂测试 MessageFactory 应该创建文本消息", "status": "passed", "title": "应该创建文本消息", "duration": 0.0809159999998883, "failureMessages": [], "location": {"line": 147, "column": 5}, "meta": {}}, {"ancestorTitles": ["数据工厂测试", "MessageFactory"], "fullName": "数据工厂测试 MessageFactory 应该创建图片消息", "status": "passed", "title": "应该创建图片消息", "duration": 0.10337499999968713, "failureMessages": [], "location": {"line": 155, "column": 5}, "meta": {}}, {"ancestorTitles": ["数据工厂测试", "ReviewFactory"], "fullName": "数据工厂测试 ReviewFactory 应该创建有效的评价数据", "status": "passed", "title": "应该创建有效的评价数据", "duration": 0.11545900000010079, "failureMessages": [], "location": {"line": 170, "column": 5}, "meta": {}}, {"ancestorTitles": ["数据工厂测试", "ReviewFactory"], "fullName": "数据工厂测试 ReviewFactory 应该创建正面评价", "status": "passed", "title": "应该创建正面评价", "duration": 0.06733299999996234, "failureMessages": [], "location": {"line": 180, "column": 5}, "meta": {}}, {"ancestorTitles": ["数据工厂测试", "ReviewFactory"], "fullName": "数据工厂测试 ReviewFactory 应该创建负面评价", "status": "passed", "title": "应该创建负面评价", "duration": 0.06741700000020501, "failureMessages": [], "location": {"line": 187, "column": 5}, "meta": {}}, {"ancestorTitles": ["数据工厂测试", "FeedbackFactory"], "fullName": "数据工厂测试 FeedbackFactory 应该创建有效的反馈数据", "status": "passed", "title": "应该创建有效的反馈数据", "duration": 0.35929200000009587, "failureMessages": [], "location": {"line": 196, "column": 5}, "meta": {}}, {"ancestorTitles": ["数据工厂测试", "FeedbackFactory"], "fullName": "数据工厂测试 FeedbackFactory 应该创建申诉反馈", "status": "passed", "title": "应该创建申诉反馈", "duration": 0.12341700000024503, "failureMessages": [], "location": {"line": 208, "column": 5}, "meta": {}}, {"ancestorTitles": ["数据工厂测试", "FeedbackFactory"], "fullName": "数据工厂测试 FeedbackFactory 应该创建Bug报告", "status": "passed", "title": "应该创建Bug报告", "duration": 0.11400000000003274, "failureMessages": [], "location": {"line": 215, "column": 5}, "meta": {}}, {"ancestorTitles": ["数据工厂测试", "DataGenerator"], "fullName": "数据工厂测试 DataGenerator 应该生成完整的用户场景", "status": "passed", "title": "应该生成完整的用户场景", "duration": 1.0519169999997757, "failureMessages": [], "location": {"line": 224, "column": 5}, "meta": {}}, {"ancestorTitles": ["数据工厂测试", "DataGenerator"], "fullName": "数据工厂测试 DataGenerator 应该生成市场数据", "status": "passed", "title": "应该生成市场数据", "duration": 1.2920829999998205, "failureMessages": [], "location": {"line": 236, "column": 5}, "meta": {}}, {"ancestorTitles": ["数据工厂测试", "数据一致性测试"], "fullName": "数据工厂测试 数据一致性测试 应该生成唯一的ID", "status": "passed", "title": "应该生成唯一的ID", "duration": 0.29858299999978044, "failureMessages": [], "location": {"line": 246, "column": 5}, "meta": {}}, {"ancestorTitles": ["数据工厂测试", "数据一致性测试"], "fullName": "数据工厂测试 数据一致性测试 应该生成有效的邮箱地址", "status": "passed", "title": "应该生成有效的邮箱地址", "duration": 0.19120799999973315, "failureMessages": [], "location": {"line": 254, "column": 5}, "meta": {}}, {"ancestorTitles": ["数据工厂测试", "数据一致性测试"], "fullName": "数据工厂测试 数据一致性测试 应该生成合理的价格范围", "status": "passed", "title": "应该生成合理的价格范围", "duration": 0.3594169999996666, "failureMessages": [], "location": {"line": 263, "column": 5}, "meta": {}}], "startTime": 1753702113129, "endTime": 1753702113138.3594, "status": "passed", "message": "", "name": "/Users/<USER>/Downloads/bitmarket-v1.3.0-Re-Made/test/factories/factory.test.ts"}, {"assertionResults": [], "startTime": 1753702110743, "endTime": 1753702110743, "status": "failed", "message": "[vitest] There was an error when mocking a module. If you are using \"vi.mock\" factory, make sure there are no top level variables inside, since this call is hoisted to top of the file. Read more: https://vitest.dev/api/vi.html#vi-mock", "name": "/Users/<USER>/Downloads/bitmarket-v1.3.0-Re-Made/test/performance/concurrent.test.ts"}, {"assertionResults": [{"ancestorTitles": ["🧠 内存优化测试套件", "📊 内存优化数据工厂测试"], "fullName": "🧠 内存优化测试套件 📊 内存优化数据工厂测试 应该显著减少内存使用", "status": "passed", "title": "应该显著减少内存使用", "duration": 4.653583000000026, "failureMessages": [], "location": {"line": 60, "column": 5}, "meta": {}}, {"ancestorTitles": ["🧠 内存优化测试套件", "📊 内存优化数据工厂测试"], "fullName": "🧠 内存优化测试套件 📊 内存优化数据工厂测试 应该有效使用对象池", "status": "passed", "title": "应该有效使用对象池", "duration": 0.5209580000000642, "failureMessages": [], "location": {"line": 105, "column": 5}, "meta": {}}, {"ancestorTitles": ["🧠 内存优化测试套件", "🌊 流式数据生成测试"], "fullName": "🧠 内存优化测试套件 🌊 流式数据生成测试 应该支持大规模流式数据生成", "status": "passed", "title": "应该支持大规模流式数据生成", "duration": 6.561208000000079, "failureMessages": [], "location": {"line": 136, "column": 5}, "meta": {}}, {"ancestorTitles": ["🧠 内存优化测试套件", "🌊 流式数据生成测试"], "fullName": "🧠 内存优化测试套件 🌊 流式数据生成测试 应该支持异步批量处理", "status": "passed", "title": "应该支持异步批量处理", "duration": 24.21216699999991, "failureMessages": [], "location": {"line": 185, "column": 5}, "meta": {}}, {"ancestorTitles": ["🧠 内存优化测试套件", "🔍 内存监控数据生成测试"], "fullName": "🧠 内存优化测试套件 🔍 内存监控数据生成测试 应该在内存监控下安全生成数据", "status": "passed", "title": "应该在内存监控下安全生成数据", "duration": 625.7269590000001, "failureMessages": [], "location": {"line": 221, "column": 5}, "meta": {}}, {"ancestorTitles": ["🧠 内存优化测试套件", "📈 内存性能对比测试"], "fullName": "🧠 内存优化测试套件 📈 内存性能对比测试 应该比标准工厂更节省内存", "status": "passed", "title": "应该比标准工厂更节省内存", "duration": 3.468667000000096, "failureMessages": [], "location": {"line": 250, "column": 5}, "meta": {}}], "startTime": 1753702111935, "endTime": 1753702112600.4688, "status": "passed", "message": "", "name": "/Users/<USER>/Downloads/bitmarket-v1.3.0-Re-Made/test/performance/memory-optimization.test.ts"}, {"assertionResults": [{"ancestorTitles": ["🚀 优化后的性能测试套件", "📊 数据生成性能优化测试"], "fullName": "🚀 优化后的性能测试套件 📊 数据生成性能优化测试 应该显著提升大批量用户数据生成速度", "status": "passed", "title": "应该显著提升大批量用户数据生成速度", "duration": 39.98858300000006, "failureMessages": [], "location": {"line": 23, "column": 5}, "meta": {}}, {"ancestorTitles": ["🚀 优化后的性能测试套件", "📊 数据生成性能优化测试"], "fullName": "🚀 优化后的性能测试套件 📊 数据生成性能优化测试 应该优化商品数据生成内存使用", "status": "passed", "title": "应该优化商品数据生成内存使用", "duration": 15.261749999999893, "failureMessages": [], "location": {"line": 36, "column": 5}, "meta": {}}, {"ancestorTitles": ["🚀 优化后的性能测试套件", "📊 数据生成性能优化测试"], "fullName": "🚀 优化后的性能测试套件 📊 数据生成性能优化测试 应该支持高效的批量数据生成", "status": "passed", "title": "应该支持高效的批量数据生成", "duration": 10.892000000000053, "failureMessages": [], "location": {"line": 48, "column": 5}, "meta": {}}, {"ancestorTitles": ["🚀 优化后的性能测试套件", "⚡ 并行处理性能测试"], "fullName": "🚀 优化后的性能测试套件 ⚡ 并行处理性能测试 应该支持高效的并行任务执行", "status": "failed", "title": "应该支持高效的并行任务执行", "duration": 21.842333000000053, "failureMessages": ["AssertionError: expected +0 to be 6 // Object.is equality\n    at /Users/<USER>/Downloads/bitmarket-v1.3.0-Re-Made/test/performance/optimized-performance.test.ts:79:32\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Users/<USER>/Downloads/bitmarket-v1.3.0-Re-Made/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected +0 to be 6 // Object.is equality\n    at /Users/<USER>/Downloads/bitmarket-v1.3.0-Re-Made/test/performance/optimized-performance.test.ts:79:32\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Users/<USER>/Downloads/bitmarket-v1.3.0-Re-Made/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected +0 to be 6 // Object.is equality\n    at /Users/<USER>/Downloads/bitmarket-v1.3.0-Re-Made/test/performance/optimized-performance.test.ts:79:32\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Users/<USER>/Downloads/bitmarket-v1.3.0-Re-Made/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 63, "column": 5}, "meta": {}}, {"ancestorTitles": ["🚀 优化后的性能测试套件", "⚡ 并行处理性能测试"], "fullName": "🚀 优化后的性能测试套件 ⚡ 并行处理性能测试 应该优化API模拟的并发性能", "status": "failed", "title": "应该优化API模拟的并发性能", "duration": 27.948458000000073, "failureMessages": ["AssertionError: expected NaN to be greater than 95\n    at /Users/<USER>/Downloads/bitmarket-v1.3.0-Re-Made/test/performance/optimized-performance.test.ts:102:33\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Users/<USER>/Downloads/bitmarket-v1.3.0-Re-Made/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected NaN to be greater than 95\n    at /Users/<USER>/Downloads/bitmarket-v1.3.0-Re-Made/test/performance/optimized-performance.test.ts:102:33\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Users/<USER>/Downloads/bitmarket-v1.3.0-Re-Made/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected NaN to be greater than 95\n    at /Users/<USER>/Downloads/bitmarket-v1.3.0-Re-Made/test/performance/optimized-performance.test.ts:102:33\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at file:///Users/<USER>/Downloads/bitmarket-v1.3.0-Re-Made/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 89, "column": 5}, "meta": {}}, {"ancestorTitles": ["🚀 优化后的性能测试套件", "🗄️ 数据库查询优化测试"], "fullName": "🚀 优化后的性能测试套件 🗄️ 数据库查询优化测试 应该优化商品搜索查询性能", "status": "failed", "title": "应该优化商品搜索查询性能", "duration": 338.158459, "failureMessages": ["AssertionError: expected 0.6666666666666666 to be greater than 0.8\n    at /Users/<USER>/Downloads/bitmarket-v1.3.0-Re-Made/test/performance/optimized-performance.test.ts:122:39\n    at file:///Users/<USER>/Downloads/bitmarket-v1.3.0-Re-Made/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected 0.6666666666666666 to be greater than 0.8\n    at /Users/<USER>/Downloads/bitmarket-v1.3.0-Re-Made/test/performance/optimized-performance.test.ts:122:39\n    at file:///Users/<USER>/Downloads/bitmarket-v1.3.0-Re-Made/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20", "AssertionError: expected 0.6666666666666666 to be greater than 0.8\n    at /Users/<USER>/Downloads/bitmarket-v1.3.0-Re-Made/test/performance/optimized-performance.test.ts:122:39\n    at file:///Users/<USER>/Downloads/bitmarket-v1.3.0-Re-Made/node_modules/@vitest/runner/dist/chunk-hooks.js:752:20"], "location": {"line": 111, "column": 5}, "meta": {}}, {"ancestorTitles": ["🚀 优化后的性能测试套件", "🗄️ 数据库查询优化测试"], "fullName": "🚀 优化后的性能测试套件 🗄️ 数据库查询优化测试 应该提供查询性能分析和优化建议", "status": "passed", "title": "应该提供查询性能分析和优化建议", "duration": 120.00229100000001, "failureMessages": [], "location": {"line": 128, "column": 5}, "meta": {}}, {"ancestorTitles": ["🚀 优化后的性能测试套件", "💾 缓存系统性能测试"], "fullName": "🚀 优化后的性能测试套件 💾 缓存系统性能测试 应该实现高性能LRU缓存", "status": "passed", "title": "应该实现高性能LRU缓存", "duration": 39.73195799999985, "failureMessages": [], "location": {"line": 144, "column": 5}, "meta": {}}, {"ancestorTitles": ["🚀 优化后的性能测试套件", "💾 缓存系统性能测试"], "fullName": "🚀 优化后的性能测试套件 💾 缓存系统性能测试 应该实现高效的多级缓存", "status": "passed", "title": "应该实现高效的多级缓存", "duration": 20.690207999999984, "failureMessages": [], "location": {"line": 168, "column": 5}, "meta": {}}, {"ancestorTitles": ["🚀 优化后的性能测试套件", "🎯 综合性能基准测试"], "fullName": "🚀 优化后的性能测试套件 🎯 综合性能基准测试 应该达到系统性能目标", "status": "passed", "title": "应该达到系统性能目标", "duration": 25.862959000000046, "failureMessages": [], "location": {"line": 203, "column": 5}, "meta": {}}, {"ancestorTitles": ["🚀 优化后的性能测试套件", "🎯 综合性能基准测试"], "fullName": "🚀 优化后的性能测试套件 🎯 综合性能基准测试 应该检测性能回归", "status": "passed", "title": "应该检测性能回归", "duration": 4.0612089999999625, "failureMessages": [], "location": {"line": 252, "column": 5}, "meta": {}}, {"ancestorTitles": ["🚀 优化后的性能测试套件", "📈 性能监控和报告"], "fullName": "🚀 优化后的性能测试套件 📈 性能监控和报告 应该生成详细的性能报告", "status": "passed", "title": "应该生成详细的性能报告", "duration": 1.6912500000000819, "failureMessages": [], "location": {"line": 272, "column": 5}, "meta": {}}, {"ancestorTitles": ["🚀 优化后的性能测试套件", "📈 性能监控和报告"], "fullName": "🚀 优化后的性能测试套件 📈 性能监控和报告 应该保存性能基线数据", "status": "passed", "title": "应该保存性能基线数据", "duration": 1.2094159999999192, "failureMessages": [], "location": {"line": 295, "column": 5}, "meta": {}}], "startTime": 1753702111669, "endTime": 1753702112337.2095, "status": "failed", "message": "", "name": "/Users/<USER>/Downloads/bitmarket-v1.3.0-Re-Made/test/performance/optimized-performance.test.ts"}, {"assertionResults": [{"ancestorTitles": ["简单性能测试", "数据生成性能"], "fullName": "简单性能测试 数据生成性能 应该快速生成大量用户数据", "status": "passed", "title": "应该快速生成大量用户数据", "duration": 24.427000000000135, "failureMessages": [], "location": {"line": 6, "column": 5}, "meta": {}}, {"ancestorTitles": ["简单性能测试", "数据生成性能"], "fullName": "简单性能测试 数据生成性能 应该快速生成大量商品数据", "status": "passed", "title": "应该快速生成大量商品数据", "duration": 7.712290999999823, "failureMessages": [], "location": {"line": 20, "column": 5}, "meta": {}}, {"ancestorTitles": ["简单性能测试", "数据生成性能"], "fullName": "简单性能测试 数据生成性能 应该快速生成大量订单数据", "status": "passed", "title": "应该快速生成大量订单数据", "duration": 5.647167000000081, "failureMessages": [], "location": {"line": 34, "column": 5}, "meta": {}}, {"ancestorTitles": ["简单性能测试", "并发操作模拟"], "fullName": "简单性能测试 并发操作模拟 应该处理并发数据库查询模拟", "status": "passed", "title": "应该处理并发数据库查询模拟", "duration": 10.91450000000009, "failureMessages": [], "location": {"line": 50, "column": 5}, "meta": {}}, {"ancestorTitles": ["简单性能测试", "并发操作模拟"], "fullName": "简单性能测试 并发操作模拟 应该处理批量API请求模拟", "status": "passed", "title": "应该处理批量API请求模拟", "duration": 0.3790409999999156, "failureMessages": [], "location": {"line": 71, "column": 5}, "meta": {}}, {"ancestorTitles": ["简单性能测试", "内存使用测试"], "fullName": "简单性能测试 内存使用测试 应该有效管理大量数据的内存使用", "status": "passed", "title": "应该有效管理大量数据的内存使用", "duration": 42.952375000000075, "failureMessages": [], "location": {"line": 94, "column": 5}, "meta": {}}, {"ancestorTitles": ["简单性能测试", "算法性能测试"], "fullName": "简单性能测试 算法性能测试 应该快速搜索大量数据", "status": "passed", "title": "应该快速搜索大量数据", "duration": 134.38029099999994, "failureMessages": [], "location": {"line": 122, "column": 5}, "meta": {}}, {"ancestorTitles": ["简单性能测试", "算法性能测试"], "fullName": "简单性能测试 算法性能测试 应该快速排序大量数据", "status": "passed", "title": "应该快速排序大量数据", "duration": 100.10508400000003, "failureMessages": [], "location": {"line": 143, "column": 5}, "meta": {}}, {"ancestorTitles": ["简单性能测试", "算法性能测试"], "fullName": "简单性能测试 算法性能测试 应该快速分页处理大量数据", "status": "passed", "title": "应该快速分页处理大量数据", "duration": 120.58229200000005, "failureMessages": [], "location": {"line": 165, "column": 5}, "meta": {}}, {"ancestorTitles": ["简单性能测试", "缓存性能模拟"], "fullName": "简单性能测试 缓存性能模拟 应该模拟缓存命中性能", "status": "passed", "title": "应该模拟缓存命中性能", "duration": 0.19366599999989376, "failureMessages": [], "location": {"line": 188, "column": 5}, "meta": {}}, {"ancestorTitles": ["简单性能测试", "缓存性能模拟"], "fullName": "简单性能测试 缓存性能模拟 应该模拟缓存未命中性能", "status": "passed", "title": "应该模拟缓存未命中性能", "duration": 0.9164580000001479, "failureMessages": [], "location": {"line": 211, "column": 5}, "meta": {}}], "startTime": 1753702112069, "endTime": 1753702112516.9165, "status": "passed", "message": "", "name": "/Users/<USER>/Downloads/bitmarket-v1.3.0-Re-Made/test/performance/simple-performance.test.ts"}, {"assertionResults": [{"ancestorTitles": ["🚀 用户数据生成性能优化测试", "📊 性能基准对比测试"], "fullName": "🚀 用户数据生成性能优化测试 📊 性能基准对比测试 应该显著提升10,000用户生成性能", "status": "passed", "title": "应该显著提升10,000用户生成性能", "duration": 34.67945800000007, "failureMessages": [], "location": {"line": 14, "column": 5}, "meta": {}}, {"ancestorTitles": ["🚀 用户数据生成性能优化测试", "📊 性能基准对比测试"], "fullName": "🚀 用户数据生成性能优化测试 📊 性能基准对比测试 应该在并行模式下实现更高性能", "status": "passed", "title": "应该在并行模式下实现更高性能", "duration": 87.54695899999979, "failureMessages": [], "location": {"line": 98, "column": 5}, "meta": {}}, {"ancestorTitles": ["🚀 用户数据生成性能优化测试", "🔍 内存使用优化测试"], "fullName": "🚀 用户数据生成性能优化测试 🔍 内存使用优化测试 应该优化大批量生成的内存使用", "status": "passed", "title": "应该优化大批量生成的内存使用", "duration": 15.666334000000006, "failureMessages": [], "location": {"line": 134, "column": 5}, "meta": {}}, {"ancestorTitles": ["🚀 用户数据生成性能优化测试", "🔍 内存使用优化测试"], "fullName": "🚀 用户数据生成性能优化测试 🔍 内存使用优化测试 应该验证内存池的效果", "status": "passed", "title": "应该验证内存池的效果", "duration": 7.596042000000125, "failureMessages": [], "location": {"line": 157, "column": 5}, "meta": {}}, {"ancestorTitles": ["🚀 用户数据生成性能优化测试", "🎯 数据质量验证"], "fullName": "🚀 用户数据生成性能优化测试 🎯 数据质量验证 应该生成高质量的用户数据", "status": "passed", "title": "应该生成高质量的用户数据", "duration": 23.352290999999923, "failureMessages": [], "location": {"line": 190, "column": 5}, "meta": {}}, {"ancestorTitles": ["🚀 用户数据生成性能优化测试", "🎯 数据质量验证"], "fullName": "🚀 用户数据生成性能优化测试 🎯 数据质量验证 应该支持专门的买家和卖家生成", "status": "passed", "title": "应该支持专门的买家和卖家生成", "duration": 1.9282080000000406, "failureMessages": [], "location": {"line": 221, "column": 5}, "meta": {}}, {"ancestorTitles": ["🚀 用户数据生成性能优化测试", "📈 极限性能测试"], "fullName": "🚀 用户数据生成性能优化测试 📈 极限性能测试 应该在极限条件下保持高性能", "status": "passed", "title": "应该在极限条件下保持高性能", "duration": 161.98570900000004, "failureMessages": [], "location": {"line": 244, "column": 5}, "meta": {}}], "startTime": 1753702112182, "endTime": 1753702112514.9856, "status": "passed", "message": "", "name": "/Users/<USER>/Downloads/bitmarket-v1.3.0-Re-Made/test/performance/user-generation-optimization.test.ts"}]}