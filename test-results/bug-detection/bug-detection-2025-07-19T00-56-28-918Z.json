{"timestamp": 1752886588954, "summary": {"total": 130, "high": 0, "medium": 126, "low": 4, "issues": [{"type": "unclosed-jsx", "file": "app\\admin\\announcements\\create\\page.tsx", "line": 25, "message": "可能未闭合的JSX标签: Record", "severity": "medium", "code": "const [errors, setErrors] = useState<Record<string, string>>({})"}, {"type": "unclosed-jsx", "file": "app\\admin\\announcements\\create\\page.tsx", "line": 27, "message": "可能未闭合的JSX标签: AnnouncementForm", "severity": "medium", "code": "const [formData, setFormData] = useState<AnnouncementForm>({"}, {"type": "unclosed-jsx", "file": "app\\admin\\announcements\\create\\page.tsx", "line": 43, "message": "可能未闭合的JSX标签: string", "severity": "medium", "code": "const newErrors: Record<string, string> = {}"}, {"type": "unclosed-jsx", "file": "app\\admin\\announcements\\create\\page.tsx", "line": 198, "message": "可能未闭合的JSX标签: textarea", "severity": "medium", "code": "<textarea"}, {"type": "unclosed-jsx", "file": "app\\admin\\announcements\\create\\page.tsx", "line": 217, "message": "可能未闭合的JSX标签: textarea", "severity": "medium", "code": "<textarea"}, {"type": "unclosed-jsx", "file": "app\\admin\\announcements\\page.tsx", "line": 59, "message": "可能未闭合的JSX标签: Announcement", "severity": "medium", "code": "const [announcements, setAnnouncements] = useState<Announcement[]>([])"}, {"type": "unclosed-jsx", "file": "app\\admin\\announcements\\[id]\\edit\\page.tsx", "line": 52, "message": "可能未闭合的JSX标签: string", "severity": "medium", "code": "const [announcementId, setAnnouncementId] = useState<string>('')"}, {"type": "unclosed-jsx", "file": "app\\admin\\announcements\\[id]\\edit\\page.tsx", "line": 55, "message": "可能未闭合的JSX标签: Record", "severity": "medium", "code": "const [errors, setErrors] = useState<Record<string, string>>({})"}, {"type": "unclosed-jsx", "file": "app\\admin\\announcements\\[id]\\edit\\page.tsx", "line": 56, "message": "可能未闭合的JSX标签: Announcement", "severity": "medium", "code": "const [announcement, setAnnouncement] = useState<Announcement | null>(null)"}, {"type": "unclosed-jsx", "file": "app\\admin\\announcements\\[id]\\edit\\page.tsx", "line": 58, "message": "可能未闭合的JSX标签: AnnouncementForm", "severity": "medium", "code": "const [formData, setFormData] = useState<AnnouncementForm>({"}, {"type": "unclosed-jsx", "file": "app\\admin\\announcements\\[id]\\edit\\page.tsx", "line": 127, "message": "可能未闭合的JSX标签: string", "severity": "medium", "code": "const newErrors: Record<string, string> = {}"}, {"type": "unclosed-jsx", "file": "app\\admin\\announcements\\[id]\\edit\\page.tsx", "line": 305, "message": "可能未闭合的JSX标签: textarea", "severity": "medium", "code": "<textarea"}, {"type": "unclosed-jsx", "file": "app\\admin\\announcements\\[id]\\edit\\page.tsx", "line": 324, "message": "可能未闭合的JSX标签: textarea", "severity": "medium", "code": "<textarea"}, {"type": "unclosed-jsx", "file": "app\\admin\\announcements\\[id]\\page.tsx", "line": 48, "message": "可能未闭合的JSX标签: string", "severity": "medium", "code": "const [announcementId, setAnnouncementId] = useState<string>('')"}, {"type": "unclosed-jsx", "file": "app\\admin\\announcements\\[id]\\page.tsx", "line": 49, "message": "可能未闭合的JSX标签: Announcement", "severity": "medium", "code": "const [announcement, setAnnouncement] = useState<Announcement | null>(null)"}, {"type": "unclosed-jsx", "file": "app\\admin\\disputes\\page.tsx", "line": 53, "message": "可能未闭合的JSX标签: Dispute", "severity": "medium", "code": "const [disputes, setDisputes] = useState<Dispute[]>([])"}, {"type": "unclosed-jsx", "file": "app\\admin\\disputes\\page.tsx", "line": 54, "message": "可能未闭合的JSX标签: DisputesResponse", "severity": "medium", "code": "const [pagination, setPagination] = useState<DisputesResponse['pagination'] | null>(null)"}, {"type": "unclosed-jsx", "file": "app\\admin\\disputes\\page.tsx", "line": 59, "message": "可能未闭合的JSX标签: string", "severity": "medium", "code": "const [actionLoading, setActionLoading] = useState<string | null>(null)"}, {"type": "unclosed-jsx", "file": "app\\admin\\escrow\\page.tsx", "line": 62, "message": "可能未闭合的JSX标签: EscrowPayment", "severity": "medium", "code": "const [escrowPayments, setEscrowPayments] = useState<EscrowPayment[]>([])"}, {"type": "unclosed-jsx", "file": "app\\admin\\escrow\\page.tsx", "line": 63, "message": "可能未闭合的JSX标签: EscrowResponse", "severity": "medium", "code": "const [pagination, setPagination] = useState<EscrowResponse['pagination'] | null>(null)"}, {"type": "unclosed-jsx", "file": "app\\admin\\escrow\\page.tsx", "line": 69, "message": "可能未闭合的JSX标签: string", "severity": "medium", "code": "const [actionLoading, setActionLoading] = useState<string | null>(null)"}, {"type": "unclosed-jsx", "file": "app\\admin\\help\\create\\page.tsx", "line": 27, "message": "可能未闭合的JSX标签: Record", "severity": "medium", "code": "const [errors, setErrors] = useState<Record<string, string>>({})"}, {"type": "unclosed-jsx", "file": "app\\admin\\help\\create\\page.tsx", "line": 28, "message": "可能未闭合的JSX标签: string", "severity": "medium", "code": "const [createdArticleId, setCreatedArticleId] = useState<string | null>(null)"}, {"type": "unclosed-jsx", "file": "app\\admin\\help\\create\\page.tsx", "line": 30, "message": "可能未闭合的JSX标签: HelpArticleForm", "severity": "medium", "code": "const [formData, setFormData] = useState<HelpArticleForm>({"}, {"type": "unclosed-jsx", "file": "app\\admin\\help\\create\\page.tsx", "line": 47, "message": "可能未闭合的JSX标签: string", "severity": "medium", "code": "const newErrors: Record<string, string> = {}"}, {"type": "unclosed-jsx", "file": "app\\admin\\help\\create\\page.tsx", "line": 213, "message": "可能未闭合的JSX标签: textarea", "severity": "medium", "code": "<textarea"}, {"type": "unclosed-jsx", "file": "app\\admin\\help\\create\\page.tsx", "line": 233, "message": "可能未闭合的JSX标签: TinyMCEEditor", "severity": "medium", "code": "<TinyMCEEditor"}, {"type": "unclosed-jsx", "file": "app\\admin\\help\\media\\page.tsx", "line": 52, "message": "可能未闭合的JSX标签: MediaFile", "severity": "medium", "code": "const [files, setFiles] = useState<MediaFile[]>([])"}, {"type": "unclosed-jsx", "file": "app\\admin\\help\\media\\page.tsx", "line": 53, "message": "可能未闭合的JSX标签: MediaStats", "severity": "medium", "code": "const [stats, setStats] = useState<MediaStats | null>(null)"}, {"type": "unclosed-jsx", "file": "app\\admin\\help\\media\\page.tsx", "line": 71, "message": "可能未闭合的JSX标签: any", "severity": "medium", "code": "const [cleanupResults, setCleanupResults] = useState<any>(null)"}, {"type": "unclosed-jsx", "file": "app\\admin\\help\\page.tsx", "line": 64, "message": "可能未闭合的JSX标签: HelpArticle", "severity": "medium", "code": "const [articles, setArticles] = useState<HelpArticle[]>([])"}, {"type": "unclosed-jsx", "file": "app\\admin\\help\\[id]\\edit\\page.tsx", "line": 51, "message": "可能未闭合的JSX标签: string", "severity": "medium", "code": "const [articleId, setArticleId] = useState<string>('')"}, {"type": "unclosed-jsx", "file": "app\\admin\\help\\[id]\\edit\\page.tsx", "line": 54, "message": "可能未闭合的JSX标签: Record", "severity": "medium", "code": "const [errors, setErrors] = useState<Record<string, string>>({})"}, {"type": "unclosed-jsx", "file": "app\\admin\\help\\[id]\\edit\\page.tsx", "line": 55, "message": "可能未闭合的JSX标签: HelpArticle", "severity": "medium", "code": "const [article, setArticle] = useState<HelpArticle | null>(null)"}, {"type": "unclosed-jsx", "file": "app\\admin\\help\\[id]\\edit\\page.tsx", "line": 57, "message": "可能未闭合的JSX标签: HelpArticleForm", "severity": "medium", "code": "const [formData, setFormData] = useState<HelpArticleForm>({"}, {"type": "unclosed-jsx", "file": "app\\admin\\help\\[id]\\edit\\page.tsx", "line": 125, "message": "可能未闭合的JSX标签: string", "severity": "medium", "code": "const newErrors: Record<string, string> = {}"}, {"type": "unclosed-jsx", "file": "app\\admin\\help\\[id]\\edit\\page.tsx", "line": 337, "message": "可能未闭合的JSX标签: textarea", "severity": "medium", "code": "<textarea"}, {"type": "unclosed-jsx", "file": "app\\admin\\help\\[id]\\edit\\page.tsx", "line": 357, "message": "可能未闭合的JSX标签: TinyMCEEditor", "severity": "medium", "code": "<TinyMCEEditor"}, {"type": "unclosed-jsx", "file": "app\\admin\\help\\[id]\\page.tsx", "line": 61, "message": "可能未闭合的JSX标签: string", "severity": "medium", "code": "const [articleId, setArticleId] = useState<string>('')"}, {"type": "unclosed-jsx", "file": "app\\admin\\help\\[id]\\page.tsx", "line": 62, "message": "可能未闭合的JSX标签: HelpArticle", "severity": "medium", "code": "const [article, setArticle] = useState<HelpArticle | null>(null)"}, {"type": "unclosed-jsx", "file": "app\\admin\\orders\\page.tsx", "line": 58, "message": "可能未闭合的JSX标签: Order", "severity": "medium", "code": "const [orders, setOrders] = useState<Order[]>([])"}, {"type": "unclosed-jsx", "file": "app\\admin\\orders\\page.tsx", "line": 59, "message": "可能未闭合的JSX标签: OrdersResponse", "severity": "medium", "code": "const [pagination, setPagination] = useState<OrdersResponse['pagination'] | null>(null)"}, {"type": "unclosed-jsx", "file": "app\\admin\\orders\\page.tsx", "line": 66, "message": "可能未闭合的JSX标签: string", "severity": "medium", "code": "const [actionLoading, setActionLoading] = useState<string | null>(null)"}, {"type": "unclosed-jsx", "file": "app\\admin\\orders\\page.tsx", "line": 139, "message": "可能未闭合的JSX标签: string", "severity": "medium", "code": "const statusMap: Record<string, string> = {"}, {"type": "unclosed-jsx", "file": "app\\admin\\orders\\page.tsx", "line": 151, "message": "可能未闭合的JSX标签: string", "severity": "medium", "code": "const colorMap: Record<string, string> = {"}, {"type": "unclosed-jsx", "file": "app\\admin\\orders\\[id]\\AdminManagementSections.tsx", "line": 21, "message": "可能未闭合的JSX标签: any", "severity": "medium", "code": "const [notes, setNotes] = useState<any[]>([])"}, {"type": "unclosed-jsx", "file": "app\\admin\\orders\\[id]\\AdminManagementSections.tsx", "line": 22, "message": "可能未闭合的JSX标签: any", "severity": "medium", "code": "const [logs, setLogs] = useState<any[]>([])"}, {"type": "unclosed-jsx", "file": "app\\admin\\orders\\[id]\\AdminManagementSections.tsx", "line": 170, "message": "可能未闭合的JSX标签: textarea", "severity": "medium", "code": "<textarea"}, {"type": "unclosed-jsx", "file": "app\\admin\\orders\\[id]\\AdminManagementSections.tsx", "line": 273, "message": "可能未闭合的JSX标签: textarea", "severity": "medium", "code": "<textarea"}, {"type": "unclosed-jsx", "file": "app\\admin\\orders\\[id]\\page.tsx", "line": 73, "message": "可能未闭合的JSX标签: Order", "severity": "medium", "code": "const [order, setOrder] = useState<Order | null>(null)"}, {"type": "unclosed-jsx", "file": "app\\admin\\orders\\[id]\\page.tsx", "line": 513, "message": "可能未闭合的JSX标签: AdminManagementSections", "severity": "medium", "code": "<AdminManagementSections"}, {"type": "unclosed-jsx", "file": "app\\admin\\page.tsx", "line": 23, "message": "可能未闭合的JSX标签: DashboardStats", "severity": "medium", "code": "const [stats, setStats] = useState<DashboardStats | null>(null)"}, {"type": "unclosed-jsx", "file": "app\\admin\\payments\\page.tsx", "line": 60, "message": "可能未闭合的JSX标签: Payment", "severity": "medium", "code": "const [payments, setPayments] = useState<Payment[]>([])"}, {"type": "unclosed-jsx", "file": "app\\admin\\payments\\page.tsx", "line": 61, "message": "可能未闭合的JSX标签: PaymentsResponse", "severity": "medium", "code": "const [pagination, setPagination] = useState<PaymentsResponse['pagination'] | null>(null)"}, {"type": "unclosed-jsx", "file": "app\\admin\\payments\\page.tsx", "line": 68, "message": "可能未闭合的JSX标签: string", "severity": "medium", "code": "const [actionLoading, setActionLoading] = useState<string | null>(null)"}, {"type": "unclosed-jsx", "file": "app\\admin\\products\\page.tsx", "line": 45, "message": "可能未闭合的JSX标签: Product", "severity": "medium", "code": "const [products, setProducts] = useState<Product[]>([])"}, {"type": "unclosed-jsx", "file": "app\\admin\\products\\page.tsx", "line": 46, "message": "可能未闭合的JSX标签: ProductsResponse", "severity": "medium", "code": "const [pagination, setPagination] = useState<ProductsResponse['pagination'] | null>(null)"}, {"type": "unclosed-jsx", "file": "app\\admin\\products\\page.tsx", "line": 53, "message": "可能未闭合的JSX标签: string", "severity": "medium", "code": "const [actionLoading, setActionLoading] = useState<string | null>(null)"}, {"type": "unclosed-jsx", "file": "app\\admin\\radar\\page.tsx", "line": 88, "message": "可能未闭合的JSX标签: Feedback", "severity": "medium", "code": "const [feedbacks, setFeedbacks] = useState<Feedback[]>([])"}, {"type": "unclosed-jsx", "file": "app\\admin\\radar\\page.tsx", "line": 89, "message": "可能未闭合的JSX标签: FeedbackStats", "severity": "medium", "code": "const [stats, setStats] = useState<FeedbackStats | null>(null)"}, {"type": "unclosed-jsx", "file": "app\\admin\\radar\\page.tsx", "line": 90, "message": "可能未闭合的JSX标签: Feedback", "severity": "medium", "code": "const [selectedFeedback, setSelectedFeedback] = useState<Feedback | null>(null)"}, {"type": "unclosed-jsx", "file": "app\\admin\\radar\\page.tsx", "line": 519, "message": "可能未闭合的JSX标签: textarea", "severity": "medium", "code": "<textarea"}, {"type": "unclosed-jsx", "file": "app\\admin\\reports\\page.tsx", "line": 79, "message": "可能未闭合的JSX标签: ReportData", "severity": "medium", "code": "const [reportData, setReportData] = useState<ReportData>({})"}, {"type": "potential-null-reference", "file": "app\\admin\\announcements\\page.tsx", "line": 211, "message": "可能的空值引用: announcements", "severity": "medium", "code": "if (isLoading && announcements.length === 0) {"}, {"type": "potential-null-reference", "file": "app\\admin\\announcements\\page.tsx", "line": 340, "message": "可能的空值引用: announcements", "severity": "medium", "code": ") : announcements.length === 0 ? ("}, {"type": "potential-null-reference", "file": "app\\admin\\announcements\\page.tsx", "line": 379, "message": "可能的空值引用: announcements", "severity": "medium", "code": "{announcements.map((announcement) => {"}, {"type": "unhandled-promise", "file": "app\\admin\\announcements\\[id]\\edit\\page.tsx", "line": 75, "message": "await调用缺少错误处理", "severity": "medium", "code": "const resolvedParams = await params"}, {"type": "unhandled-promise", "file": "app\\admin\\announcements\\[id]\\page.tsx", "line": 56, "message": "await调用缺少错误处理", "severity": "medium", "code": "const resolvedParams = await params"}, {"type": "potential-null-reference", "file": "app\\admin\\disputes\\page.tsx", "line": 91, "message": "可能的空值引用: data", "severity": "medium", "code": "setDisputes(data.disputes)"}, {"type": "potential-null-reference", "file": "app\\admin\\disputes\\page.tsx", "line": 92, "message": "可能的空值引用: data", "severity": "medium", "code": "setPagination(data.pagination)"}, {"type": "potential-null-reference", "file": "app\\admin\\disputes\\page.tsx", "line": 260, "message": "可能的空值引用: disputes", "severity": "medium", "code": "{disputes.map((dispute) => ("}, {"type": "potential-null-reference", "file": "app\\admin\\disputes\\page.tsx", "line": 367, "message": "可能的空值引用: disputes", "severity": "medium", "code": "{disputes.length === 0 && ("}, {"type": "potential-null-reference", "file": "app\\admin\\escrow\\page.tsx", "line": 102, "message": "可能的空值引用: data", "severity": "medium", "code": "setEscrowPayments(data.escrowPayments)"}, {"type": "potential-null-reference", "file": "app\\admin\\escrow\\page.tsx", "line": 103, "message": "可能的空值引用: data", "severity": "medium", "code": "setPagination(data.pagination)"}, {"type": "unsafe-array-access", "file": "app\\admin\\escrow\\page.tsx", "line": 177, "message": "不安全的数组访问: statusMap[status]", "severity": "low", "code": "return statusMap[status] || status"}, {"type": "unsafe-array-access", "file": "app\\admin\\escrow\\page.tsx", "line": 188, "message": "不安全的数组访问: colorMap[status]", "severity": "low", "code": "return colorMap[status] || 'bg-gray-100 text-gray-800'"}, {"type": "unsafe-array-access", "file": "app\\admin\\escrow\\page.tsx", "line": 197, "message": "不安全的数组访问: methodMap[method]", "severity": "low", "code": "return methodMap[method] || method"}, {"type": "potential-null-reference", "file": "app\\admin\\escrow\\page.tsx", "line": 317, "message": "可能的空值引用: escrow", "severity": "medium", "code": "<div key={escrow.id} className=\"bg-white shadow rounded-lg p-6\">"}, {"type": "potential-null-reference", "file": "app\\admin\\escrow\\page.tsx", "line": 323, "message": "可能的空值引用: escrow", "severity": "medium", "code": "<div><span className=\"font-medium\">托管ID:</span> {escrow.id.slice(-8)}</div>"}, {"type": "potential-null-reference", "file": "app\\admin\\escrow\\page.tsx", "line": 324, "message": "可能的空值引用: escrow", "severity": "medium", "code": "<div><span className=\"font-medium\">金额:</span> {formatCurrency(escrow.amount)}</div>"}, {"type": "potential-null-reference", "file": "app\\admin\\escrow\\page.tsx", "line": 325, "message": "可能的空值引用: escrow", "severity": "medium", "code": "<div><span className=\"font-medium\">平台手续费:</span> {formatCurrency(escrow.platformFee)}</div>"}, {"type": "potential-null-reference", "file": "app\\admin\\escrow\\page.tsx", "line": 326, "message": "可能的空值引用: escrow", "severity": "medium", "code": "<div><span className=\"font-medium\">支付方式:</span> {getPaymentMethodText(escrow.paymentMethod)}</div>"}, {"type": "potential-null-reference", "file": "app\\admin\\escrow\\page.tsx", "line": 327, "message": "可能的空值引用: escrow", "severity": "medium", "code": "<div><span className=\"font-medium\">创建时间:</span> {formatDate(escrow.createdAt.toString())}</div>"}, {"type": "potential-null-reference", "file": "app\\admin\\escrow\\page.tsx", "line": 328, "message": "可能的空值引用: escrow", "severity": "medium", "code": "{escrow.txHash && ("}, {"type": "potential-null-reference", "file": "app\\admin\\escrow\\page.tsx", "line": 330, "message": "可能的空值引用: escrow", "severity": "medium", "code": "<span className=\"text-xs font-mono block mt-1 break-all\">{escrow.txHash}</span>"}, {"type": "potential-null-reference", "file": "app\\admin\\escrow\\page.tsx", "line": 340, "message": "可能的空值引用: escrow", "severity": "medium", "code": "<div><span className=\"font-medium\">订单号:</span> {escrow.order.orderNumber}</div>"}, {"type": "potential-null-reference", "file": "app\\admin\\escrow\\page.tsx", "line": 341, "message": "可能的空值引用: escrow", "severity": "medium", "code": "<div><span className=\"font-medium\">商品:</span> {escrow.order.product.title}</div>"}, {"type": "potential-null-reference", "file": "app\\admin\\escrow\\page.tsx", "line": 342, "message": "可能的空值引用: escrow", "severity": "medium", "code": "<div><span className=\"font-medium\">商品价格:</span> {formatCurrency(escrow.order.product.price)}</div>"}, {"type": "potential-null-reference", "file": "app\\admin\\escrow\\page.tsx", "line": 345, "message": "可能的空值引用: escrow", "severity": "medium", "code": "{escrow.order.status}"}, {"type": "potential-null-reference", "file": "app\\admin\\escrow\\page.tsx", "line": 358, "message": "可能的空值引用: escrow", "severity": "medium", "code": "{escrow.order.buyer.name || '未设置'} ({escrow.order.buyer.email})"}, {"type": "potential-null-reference", "file": "app\\admin\\escrow\\page.tsx", "line": 361, "message": "可能的空值引用: escrow", "severity": "medium", "code": "信用分: {escrow.order.buyer.creditScore}"}, {"type": "potential-null-reference", "file": "app\\admin\\escrow\\page.tsx", "line": 367, "message": "可能的空值引用: escrow", "severity": "medium", "code": "{escrow.order.seller.name || '未设置'} ({escrow.order.seller.email})"}, {"type": "potential-null-reference", "file": "app\\admin\\escrow\\page.tsx", "line": 370, "message": "可能的空值引用: escrow", "severity": "medium", "code": "信用分: {escrow.order.seller.creditScore}"}, {"type": "potential-null-reference", "file": "app\\admin\\escrow\\page.tsx", "line": 380, "message": "可能的空值引用: escrow", "severity": "medium", "code": "<span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusBadgeColor(escrow.status)}`}>"}, {"type": "potential-null-reference", "file": "app\\admin\\escrow\\page.tsx", "line": 381, "message": "可能的空值引用: escrow", "severity": "medium", "code": "{getStatusText(escrow.status)}"}, {"type": "potential-null-reference", "file": "app\\admin\\escrow\\page.tsx", "line": 386, "message": "可能的空值引用: escrow", "severity": "medium", "code": "{escrow.status === 'PENDING' && ("}, {"type": "potential-null-reference", "file": "app\\admin\\escrow\\page.tsx", "line": 388, "message": "可能的空值引用: escrow", "severity": "medium", "code": "onClick={() => handleEscrowAction(escrow.id, 'confirmFunding', { notes: '管理员确认资金到账' })}"}, {"type": "potential-null-reference", "file": "app\\admin\\escrow\\page.tsx", "line": 389, "message": "可能的空值引用: escrow", "severity": "medium", "code": "disabled={actionLoading === escrow.id}"}, {"type": "potential-null-reference", "file": "app\\admin\\escrow\\page.tsx", "line": 392, "message": "可能的空值引用: escrow", "severity": "medium", "code": "{actionLoading === escrow.id ? '处理中...' : '确认到账'}"}, {"type": "potential-null-reference", "file": "app\\admin\\escrow\\page.tsx", "line": 396, "message": "可能的空值引用: escrow", "severity": "medium", "code": "{escrow.status === 'FUNDED' && ("}, {"type": "potential-null-reference", "file": "app\\admin\\escrow\\page.tsx", "line": 399, "message": "可能的空值引用: escrow", "severity": "medium", "code": "onClick={() => handleEscrowAction(escrow.id, 'releaseFunds', { notes: '管理员释放资金给卖家' })}"}, {"type": "potential-null-reference", "file": "app\\admin\\escrow\\page.tsx", "line": 400, "message": "可能的空值引用: escrow", "severity": "medium", "code": "disabled={actionLoading === escrow.id}"}, {"type": "potential-null-reference", "file": "app\\admin\\escrow\\page.tsx", "line": 403, "message": "可能的空值引用: escrow", "severity": "medium", "code": "{actionLoading === escrow.id ? '处理中...' : '释放资金'}"}, {"type": "potential-null-reference", "file": "app\\admin\\escrow\\page.tsx", "line": 407, "message": "可能的空值引用: escrow", "severity": "medium", "code": "const refundAmount = prompt('请输入退款金额 (USDT):', escrow.amount.toString())"}, {"type": "potential-null-reference", "file": "app\\admin\\escrow\\page.tsx", "line": 409, "message": "可能的空值引用: escrow", "severity": "medium", "code": "handleEscrowAction(escrow.id, 'refundBuyer', {"}, {"type": "potential-null-reference", "file": "app\\admin\\escrow\\page.tsx", "line": 415, "message": "可能的空值引用: escrow", "severity": "medium", "code": "disabled={actionLoading === escrow.id}"}, {"type": "potential-null-reference", "file": "app\\admin\\escrow\\page.tsx", "line": 418, "message": "可能的空值引用: escrow", "severity": "medium", "code": "{actionLoading === escrow.id ? '处理中...' : '退款买家'}"}, {"type": "potential-null-reference", "file": "app\\admin\\escrow\\page.tsx", "line": 425, "message": "可能的空值引用: escrow", "severity": "medium", "code": "const notes = prompt('请输入管理员备注:', escrow.adminNotes || '')"}, {"type": "potential-null-reference", "file": "app\\admin\\escrow\\page.tsx", "line": 427, "message": "可能的空值引用: escrow", "severity": "medium", "code": "handleEscrowAction(escrow.id, 'addNotes', { notes })"}, {"type": "potential-null-reference", "file": "app\\admin\\escrow\\page.tsx", "line": 438, "message": "可能的空值引用: escrow", "severity": "medium", "code": "{escrow.fundedAt && ("}, {"type": "potential-null-reference", "file": "app\\admin\\escrow\\page.tsx", "line": 439, "message": "可能的空值引用: escrow", "severity": "medium", "code": "<div>到账时间: {formatDate(escrow.fundedAt.toString())}</div>"}, {"type": "potential-null-reference", "file": "app\\admin\\escrow\\page.tsx", "line": 441, "message": "可能的空值引用: escrow", "severity": "medium", "code": "{escrow.releasedAt && ("}, {"type": "potential-null-reference", "file": "app\\admin\\escrow\\page.tsx", "line": 442, "message": "可能的空值引用: escrow", "severity": "medium", "code": "<div>释放时间: {formatDate(escrow.releasedAt.toString())}</div>"}, {"type": "potential-null-reference", "file": "app\\admin\\escrow\\page.tsx", "line": 444, "message": "可能的空值引用: escrow", "severity": "medium", "code": "{escrow.refundedAt && ("}, {"type": "potential-null-reference", "file": "app\\admin\\escrow\\page.tsx", "line": 445, "message": "可能的空值引用: escrow", "severity": "medium", "code": "<div>退款时间: {formatDate(escrow.refundedAt.toString())}</div>"}, {"type": "potential-null-reference", "file": "app\\admin\\escrow\\page.tsx", "line": 450, "message": "可能的空值引用: escrow", "severity": "medium", "code": "{escrow.adminNotes && ("}, {"type": "potential-null-reference", "file": "app\\admin\\escrow\\page.tsx", "line": 452, "message": "可能的空值引用: escrow", "severity": "medium", "code": "<strong>备注:</strong> {escrow.adminNotes}"}, {"type": "potential-null-reference", "file": "app\\admin\\help\\media\\page.tsx", "line": 123, "message": "可能的空值引用: data", "severity": "medium", "code": "setFiles(data.files)"}, {"type": "potential-null-reference", "file": "app\\admin\\help\\media\\page.tsx", "line": 124, "message": "可能的空值引用: data", "severity": "medium", "code": "setPagination(data.pagination)"}, {"type": "potential-null-reference", "file": "app\\admin\\help\\media\\page.tsx", "line": 143, "message": "可能的空值引用: data", "severity": "medium", "code": "setStats(data.statistics)"}, {"type": "potential-null-reference", "file": "app\\admin\\help\\media\\page.tsx", "line": 167, "message": "可能的空值引用: data", "severity": "medium", "code": "alert(data.error || '删除失败')"}, {"type": "unhandled-promise", "file": "app\\admin\\help\\media\\page.tsx", "line": 191, "message": "await调用缺少错误处理", "severity": "medium", "code": "const data = await response.json()"}, {"type": "potential-null-reference", "file": "app\\admin\\help\\media\\page.tsx", "line": 192, "message": "可能的空值引用: data", "severity": "medium", "code": "setCleanupResults(data.results)"}, {"type": "potential-null-reference", "file": "app\\admin\\help\\media\\page.tsx", "line": 195, "message": "可能的空值引用: data", "severity": "medium", "code": "alert(`清理完成！删除了 ${data.results.summary.totalDeleted} 个文件`)"}, {"type": "potential-null-reference", "file": "app\\admin\\help\\media\\page.tsx", "line": 201, "message": "可能的空值引用: data", "severity": "medium", "code": "alert(data.error || '清理失败')"}, {"type": "unsafe-array-access", "file": "app\\admin\\help\\media\\page.tsx", "line": 231, "message": "不安全的数组访问: sizes[i]", "severity": "low", "code": "return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]"}, {"type": "potential-null-reference", "file": "app\\admin\\help\\media\\page.tsx", "line": 363, "message": "可能的空值引用: e", "severity": "medium", "code": "onChange={(e) => setTypeFilter(e.target.value)}"}, {"type": "potential-null-reference", "file": "app\\admin\\help\\media\\page.tsx", "line": 379, "message": "可能的空值引用: e", "severity": "medium", "code": "onChange={(e) => setStatusFilter(e.target.value)}"}, {"type": "potential-null-reference", "file": "app\\admin\\help\\media\\page.tsx", "line": 399, "message": "可能的空值引用: e", "severity": "medium", "code": "onChange={(e) => setSearchQuery(e.target.value)}"}, {"type": "potential-null-reference", "file": "app\\admin\\help\\media\\page.tsx", "line": 400, "message": "可能的空值引用: e", "severity": "medium", "code": "onKeyPress={(e) => e.key === 'Enter' && handleSearch()}"}]}, "details": {"syntax": [{"type": "unclosed-jsx", "file": "app\\admin\\announcements\\create\\page.tsx", "line": 25, "message": "可能未闭合的JSX标签: Record", "severity": "medium", "code": "const [errors, setErrors] = useState<Record<string, string>>({})"}, {"type": "unclosed-jsx", "file": "app\\admin\\announcements\\create\\page.tsx", "line": 27, "message": "可能未闭合的JSX标签: AnnouncementForm", "severity": "medium", "code": "const [formData, setFormData] = useState<AnnouncementForm>({"}, {"type": "unclosed-jsx", "file": "app\\admin\\announcements\\create\\page.tsx", "line": 43, "message": "可能未闭合的JSX标签: string", "severity": "medium", "code": "const newErrors: Record<string, string> = {}"}, {"type": "unclosed-jsx", "file": "app\\admin\\announcements\\create\\page.tsx", "line": 198, "message": "可能未闭合的JSX标签: textarea", "severity": "medium", "code": "<textarea"}, {"type": "unclosed-jsx", "file": "app\\admin\\announcements\\create\\page.tsx", "line": 217, "message": "可能未闭合的JSX标签: textarea", "severity": "medium", "code": "<textarea"}, {"type": "unclosed-jsx", "file": "app\\admin\\announcements\\page.tsx", "line": 59, "message": "可能未闭合的JSX标签: Announcement", "severity": "medium", "code": "const [announcements, setAnnouncements] = useState<Announcement[]>([])"}, {"type": "unclosed-jsx", "file": "app\\admin\\announcements\\[id]\\edit\\page.tsx", "line": 52, "message": "可能未闭合的JSX标签: string", "severity": "medium", "code": "const [announcementId, setAnnouncementId] = useState<string>('')"}, {"type": "unclosed-jsx", "file": "app\\admin\\announcements\\[id]\\edit\\page.tsx", "line": 55, "message": "可能未闭合的JSX标签: Record", "severity": "medium", "code": "const [errors, setErrors] = useState<Record<string, string>>({})"}, {"type": "unclosed-jsx", "file": "app\\admin\\announcements\\[id]\\edit\\page.tsx", "line": 56, "message": "可能未闭合的JSX标签: Announcement", "severity": "medium", "code": "const [announcement, setAnnouncement] = useState<Announcement | null>(null)"}, {"type": "unclosed-jsx", "file": "app\\admin\\announcements\\[id]\\edit\\page.tsx", "line": 58, "message": "可能未闭合的JSX标签: AnnouncementForm", "severity": "medium", "code": "const [formData, setFormData] = useState<AnnouncementForm>({"}, {"type": "unclosed-jsx", "file": "app\\admin\\announcements\\[id]\\edit\\page.tsx", "line": 127, "message": "可能未闭合的JSX标签: string", "severity": "medium", "code": "const newErrors: Record<string, string> = {}"}, {"type": "unclosed-jsx", "file": "app\\admin\\announcements\\[id]\\edit\\page.tsx", "line": 305, "message": "可能未闭合的JSX标签: textarea", "severity": "medium", "code": "<textarea"}, {"type": "unclosed-jsx", "file": "app\\admin\\announcements\\[id]\\edit\\page.tsx", "line": 324, "message": "可能未闭合的JSX标签: textarea", "severity": "medium", "code": "<textarea"}, {"type": "unclosed-jsx", "file": "app\\admin\\announcements\\[id]\\page.tsx", "line": 48, "message": "可能未闭合的JSX标签: string", "severity": "medium", "code": "const [announcementId, setAnnouncementId] = useState<string>('')"}, {"type": "unclosed-jsx", "file": "app\\admin\\announcements\\[id]\\page.tsx", "line": 49, "message": "可能未闭合的JSX标签: Announcement", "severity": "medium", "code": "const [announcement, setAnnouncement] = useState<Announcement | null>(null)"}, {"type": "unclosed-jsx", "file": "app\\admin\\disputes\\page.tsx", "line": 53, "message": "可能未闭合的JSX标签: Dispute", "severity": "medium", "code": "const [disputes, setDisputes] = useState<Dispute[]>([])"}, {"type": "unclosed-jsx", "file": "app\\admin\\disputes\\page.tsx", "line": 54, "message": "可能未闭合的JSX标签: DisputesResponse", "severity": "medium", "code": "const [pagination, setPagination] = useState<DisputesResponse['pagination'] | null>(null)"}, {"type": "unclosed-jsx", "file": "app\\admin\\disputes\\page.tsx", "line": 59, "message": "可能未闭合的JSX标签: string", "severity": "medium", "code": "const [actionLoading, setActionLoading] = useState<string | null>(null)"}, {"type": "unclosed-jsx", "file": "app\\admin\\escrow\\page.tsx", "line": 62, "message": "可能未闭合的JSX标签: EscrowPayment", "severity": "medium", "code": "const [escrowPayments, setEscrowPayments] = useState<EscrowPayment[]>([])"}, {"type": "unclosed-jsx", "file": "app\\admin\\escrow\\page.tsx", "line": 63, "message": "可能未闭合的JSX标签: EscrowResponse", "severity": "medium", "code": "const [pagination, setPagination] = useState<EscrowResponse['pagination'] | null>(null)"}, {"type": "unclosed-jsx", "file": "app\\admin\\escrow\\page.tsx", "line": 69, "message": "可能未闭合的JSX标签: string", "severity": "medium", "code": "const [actionLoading, setActionLoading] = useState<string | null>(null)"}, {"type": "unclosed-jsx", "file": "app\\admin\\help\\create\\page.tsx", "line": 27, "message": "可能未闭合的JSX标签: Record", "severity": "medium", "code": "const [errors, setErrors] = useState<Record<string, string>>({})"}, {"type": "unclosed-jsx", "file": "app\\admin\\help\\create\\page.tsx", "line": 28, "message": "可能未闭合的JSX标签: string", "severity": "medium", "code": "const [createdArticleId, setCreatedArticleId] = useState<string | null>(null)"}, {"type": "unclosed-jsx", "file": "app\\admin\\help\\create\\page.tsx", "line": 30, "message": "可能未闭合的JSX标签: HelpArticleForm", "severity": "medium", "code": "const [formData, setFormData] = useState<HelpArticleForm>({"}, {"type": "unclosed-jsx", "file": "app\\admin\\help\\create\\page.tsx", "line": 47, "message": "可能未闭合的JSX标签: string", "severity": "medium", "code": "const newErrors: Record<string, string> = {}"}, {"type": "unclosed-jsx", "file": "app\\admin\\help\\create\\page.tsx", "line": 213, "message": "可能未闭合的JSX标签: textarea", "severity": "medium", "code": "<textarea"}, {"type": "unclosed-jsx", "file": "app\\admin\\help\\create\\page.tsx", "line": 233, "message": "可能未闭合的JSX标签: TinyMCEEditor", "severity": "medium", "code": "<TinyMCEEditor"}, {"type": "unclosed-jsx", "file": "app\\admin\\help\\media\\page.tsx", "line": 52, "message": "可能未闭合的JSX标签: MediaFile", "severity": "medium", "code": "const [files, setFiles] = useState<MediaFile[]>([])"}, {"type": "unclosed-jsx", "file": "app\\admin\\help\\media\\page.tsx", "line": 53, "message": "可能未闭合的JSX标签: MediaStats", "severity": "medium", "code": "const [stats, setStats] = useState<MediaStats | null>(null)"}, {"type": "unclosed-jsx", "file": "app\\admin\\help\\media\\page.tsx", "line": 71, "message": "可能未闭合的JSX标签: any", "severity": "medium", "code": "const [cleanupResults, setCleanupResults] = useState<any>(null)"}, {"type": "unclosed-jsx", "file": "app\\admin\\help\\page.tsx", "line": 64, "message": "可能未闭合的JSX标签: HelpArticle", "severity": "medium", "code": "const [articles, setArticles] = useState<HelpArticle[]>([])"}, {"type": "unclosed-jsx", "file": "app\\admin\\help\\[id]\\edit\\page.tsx", "line": 51, "message": "可能未闭合的JSX标签: string", "severity": "medium", "code": "const [articleId, setArticleId] = useState<string>('')"}, {"type": "unclosed-jsx", "file": "app\\admin\\help\\[id]\\edit\\page.tsx", "line": 54, "message": "可能未闭合的JSX标签: Record", "severity": "medium", "code": "const [errors, setErrors] = useState<Record<string, string>>({})"}, {"type": "unclosed-jsx", "file": "app\\admin\\help\\[id]\\edit\\page.tsx", "line": 55, "message": "可能未闭合的JSX标签: HelpArticle", "severity": "medium", "code": "const [article, setArticle] = useState<HelpArticle | null>(null)"}, {"type": "unclosed-jsx", "file": "app\\admin\\help\\[id]\\edit\\page.tsx", "line": 57, "message": "可能未闭合的JSX标签: HelpArticleForm", "severity": "medium", "code": "const [formData, setFormData] = useState<HelpArticleForm>({"}, {"type": "unclosed-jsx", "file": "app\\admin\\help\\[id]\\edit\\page.tsx", "line": 125, "message": "可能未闭合的JSX标签: string", "severity": "medium", "code": "const newErrors: Record<string, string> = {}"}, {"type": "unclosed-jsx", "file": "app\\admin\\help\\[id]\\edit\\page.tsx", "line": 337, "message": "可能未闭合的JSX标签: textarea", "severity": "medium", "code": "<textarea"}, {"type": "unclosed-jsx", "file": "app\\admin\\help\\[id]\\edit\\page.tsx", "line": 357, "message": "可能未闭合的JSX标签: TinyMCEEditor", "severity": "medium", "code": "<TinyMCEEditor"}, {"type": "unclosed-jsx", "file": "app\\admin\\help\\[id]\\page.tsx", "line": 61, "message": "可能未闭合的JSX标签: string", "severity": "medium", "code": "const [articleId, setArticleId] = useState<string>('')"}, {"type": "unclosed-jsx", "file": "app\\admin\\help\\[id]\\page.tsx", "line": 62, "message": "可能未闭合的JSX标签: HelpArticle", "severity": "medium", "code": "const [article, setArticle] = useState<HelpArticle | null>(null)"}, {"type": "unclosed-jsx", "file": "app\\admin\\orders\\page.tsx", "line": 58, "message": "可能未闭合的JSX标签: Order", "severity": "medium", "code": "const [orders, setOrders] = useState<Order[]>([])"}, {"type": "unclosed-jsx", "file": "app\\admin\\orders\\page.tsx", "line": 59, "message": "可能未闭合的JSX标签: OrdersResponse", "severity": "medium", "code": "const [pagination, setPagination] = useState<OrdersResponse['pagination'] | null>(null)"}, {"type": "unclosed-jsx", "file": "app\\admin\\orders\\page.tsx", "line": 66, "message": "可能未闭合的JSX标签: string", "severity": "medium", "code": "const [actionLoading, setActionLoading] = useState<string | null>(null)"}, {"type": "unclosed-jsx", "file": "app\\admin\\orders\\page.tsx", "line": 139, "message": "可能未闭合的JSX标签: string", "severity": "medium", "code": "const statusMap: Record<string, string> = {"}, {"type": "unclosed-jsx", "file": "app\\admin\\orders\\page.tsx", "line": 151, "message": "可能未闭合的JSX标签: string", "severity": "medium", "code": "const colorMap: Record<string, string> = {"}, {"type": "unclosed-jsx", "file": "app\\admin\\orders\\[id]\\AdminManagementSections.tsx", "line": 21, "message": "可能未闭合的JSX标签: any", "severity": "medium", "code": "const [notes, setNotes] = useState<any[]>([])"}, {"type": "unclosed-jsx", "file": "app\\admin\\orders\\[id]\\AdminManagementSections.tsx", "line": 22, "message": "可能未闭合的JSX标签: any", "severity": "medium", "code": "const [logs, setLogs] = useState<any[]>([])"}, {"type": "unclosed-jsx", "file": "app\\admin\\orders\\[id]\\AdminManagementSections.tsx", "line": 170, "message": "可能未闭合的JSX标签: textarea", "severity": "medium", "code": "<textarea"}, {"type": "unclosed-jsx", "file": "app\\admin\\orders\\[id]\\AdminManagementSections.tsx", "line": 273, "message": "可能未闭合的JSX标签: textarea", "severity": "medium", "code": "<textarea"}, {"type": "unclosed-jsx", "file": "app\\admin\\orders\\[id]\\page.tsx", "line": 73, "message": "可能未闭合的JSX标签: Order", "severity": "medium", "code": "const [order, setOrder] = useState<Order | null>(null)"}, {"type": "unclosed-jsx", "file": "app\\admin\\orders\\[id]\\page.tsx", "line": 513, "message": "可能未闭合的JSX标签: AdminManagementSections", "severity": "medium", "code": "<AdminManagementSections"}, {"type": "unclosed-jsx", "file": "app\\admin\\page.tsx", "line": 23, "message": "可能未闭合的JSX标签: DashboardStats", "severity": "medium", "code": "const [stats, setStats] = useState<DashboardStats | null>(null)"}, {"type": "unclosed-jsx", "file": "app\\admin\\payments\\page.tsx", "line": 60, "message": "可能未闭合的JSX标签: Payment", "severity": "medium", "code": "const [payments, setPayments] = useState<Payment[]>([])"}, {"type": "unclosed-jsx", "file": "app\\admin\\payments\\page.tsx", "line": 61, "message": "可能未闭合的JSX标签: PaymentsResponse", "severity": "medium", "code": "const [pagination, setPagination] = useState<PaymentsResponse['pagination'] | null>(null)"}, {"type": "unclosed-jsx", "file": "app\\admin\\payments\\page.tsx", "line": 68, "message": "可能未闭合的JSX标签: string", "severity": "medium", "code": "const [actionLoading, setActionLoading] = useState<string | null>(null)"}, {"type": "unclosed-jsx", "file": "app\\admin\\products\\page.tsx", "line": 45, "message": "可能未闭合的JSX标签: Product", "severity": "medium", "code": "const [products, setProducts] = useState<Product[]>([])"}, {"type": "unclosed-jsx", "file": "app\\admin\\products\\page.tsx", "line": 46, "message": "可能未闭合的JSX标签: ProductsResponse", "severity": "medium", "code": "const [pagination, setPagination] = useState<ProductsResponse['pagination'] | null>(null)"}, {"type": "unclosed-jsx", "file": "app\\admin\\products\\page.tsx", "line": 53, "message": "可能未闭合的JSX标签: string", "severity": "medium", "code": "const [actionLoading, setActionLoading] = useState<string | null>(null)"}, {"type": "unclosed-jsx", "file": "app\\admin\\radar\\page.tsx", "line": 88, "message": "可能未闭合的JSX标签: Feedback", "severity": "medium", "code": "const [feedbacks, setFeedbacks] = useState<Feedback[]>([])"}, {"type": "unclosed-jsx", "file": "app\\admin\\radar\\page.tsx", "line": 89, "message": "可能未闭合的JSX标签: FeedbackStats", "severity": "medium", "code": "const [stats, setStats] = useState<FeedbackStats | null>(null)"}, {"type": "unclosed-jsx", "file": "app\\admin\\radar\\page.tsx", "line": 90, "message": "可能未闭合的JSX标签: Feedback", "severity": "medium", "code": "const [selectedFeedback, setSelectedFeedback] = useState<Feedback | null>(null)"}, {"type": "unclosed-jsx", "file": "app\\admin\\radar\\page.tsx", "line": 519, "message": "可能未闭合的JSX标签: textarea", "severity": "medium", "code": "<textarea"}, {"type": "unclosed-jsx", "file": "app\\admin\\reports\\page.tsx", "line": 79, "message": "可能未闭合的JSX标签: ReportData", "severity": "medium", "code": "const [reportData, setReportData] = useState<ReportData>({})"}], "imports": [], "types": [], "runtime": [{"type": "potential-null-reference", "file": "app\\admin\\announcements\\page.tsx", "line": 211, "message": "可能的空值引用: announcements", "severity": "medium", "code": "if (isLoading && announcements.length === 0) {"}, {"type": "potential-null-reference", "file": "app\\admin\\announcements\\page.tsx", "line": 340, "message": "可能的空值引用: announcements", "severity": "medium", "code": ") : announcements.length === 0 ? ("}, {"type": "potential-null-reference", "file": "app\\admin\\announcements\\page.tsx", "line": 379, "message": "可能的空值引用: announcements", "severity": "medium", "code": "{announcements.map((announcement) => {"}, {"type": "unhandled-promise", "file": "app\\admin\\announcements\\[id]\\edit\\page.tsx", "line": 75, "message": "await调用缺少错误处理", "severity": "medium", "code": "const resolvedParams = await params"}, {"type": "unhandled-promise", "file": "app\\admin\\announcements\\[id]\\page.tsx", "line": 56, "message": "await调用缺少错误处理", "severity": "medium", "code": "const resolvedParams = await params"}, {"type": "potential-null-reference", "file": "app\\admin\\disputes\\page.tsx", "line": 91, "message": "可能的空值引用: data", "severity": "medium", "code": "setDisputes(data.disputes)"}, {"type": "potential-null-reference", "file": "app\\admin\\disputes\\page.tsx", "line": 92, "message": "可能的空值引用: data", "severity": "medium", "code": "setPagination(data.pagination)"}, {"type": "potential-null-reference", "file": "app\\admin\\disputes\\page.tsx", "line": 260, "message": "可能的空值引用: disputes", "severity": "medium", "code": "{disputes.map((dispute) => ("}, {"type": "potential-null-reference", "file": "app\\admin\\disputes\\page.tsx", "line": 367, "message": "可能的空值引用: disputes", "severity": "medium", "code": "{disputes.length === 0 && ("}, {"type": "potential-null-reference", "file": "app\\admin\\escrow\\page.tsx", "line": 102, "message": "可能的空值引用: data", "severity": "medium", "code": "setEscrowPayments(data.escrowPayments)"}, {"type": "potential-null-reference", "file": "app\\admin\\escrow\\page.tsx", "line": 103, "message": "可能的空值引用: data", "severity": "medium", "code": "setPagination(data.pagination)"}, {"type": "unsafe-array-access", "file": "app\\admin\\escrow\\page.tsx", "line": 177, "message": "不安全的数组访问: statusMap[status]", "severity": "low", "code": "return statusMap[status] || status"}, {"type": "unsafe-array-access", "file": "app\\admin\\escrow\\page.tsx", "line": 188, "message": "不安全的数组访问: colorMap[status]", "severity": "low", "code": "return colorMap[status] || 'bg-gray-100 text-gray-800'"}, {"type": "unsafe-array-access", "file": "app\\admin\\escrow\\page.tsx", "line": 197, "message": "不安全的数组访问: methodMap[method]", "severity": "low", "code": "return methodMap[method] || method"}, {"type": "potential-null-reference", "file": "app\\admin\\escrow\\page.tsx", "line": 317, "message": "可能的空值引用: escrow", "severity": "medium", "code": "<div key={escrow.id} className=\"bg-white shadow rounded-lg p-6\">"}, {"type": "potential-null-reference", "file": "app\\admin\\escrow\\page.tsx", "line": 323, "message": "可能的空值引用: escrow", "severity": "medium", "code": "<div><span className=\"font-medium\">托管ID:</span> {escrow.id.slice(-8)}</div>"}, {"type": "potential-null-reference", "file": "app\\admin\\escrow\\page.tsx", "line": 324, "message": "可能的空值引用: escrow", "severity": "medium", "code": "<div><span className=\"font-medium\">金额:</span> {formatCurrency(escrow.amount)}</div>"}, {"type": "potential-null-reference", "file": "app\\admin\\escrow\\page.tsx", "line": 325, "message": "可能的空值引用: escrow", "severity": "medium", "code": "<div><span className=\"font-medium\">平台手续费:</span> {formatCurrency(escrow.platformFee)}</div>"}, {"type": "potential-null-reference", "file": "app\\admin\\escrow\\page.tsx", "line": 326, "message": "可能的空值引用: escrow", "severity": "medium", "code": "<div><span className=\"font-medium\">支付方式:</span> {getPaymentMethodText(escrow.paymentMethod)}</div>"}, {"type": "potential-null-reference", "file": "app\\admin\\escrow\\page.tsx", "line": 327, "message": "可能的空值引用: escrow", "severity": "medium", "code": "<div><span className=\"font-medium\">创建时间:</span> {formatDate(escrow.createdAt.toString())}</div>"}, {"type": "potential-null-reference", "file": "app\\admin\\escrow\\page.tsx", "line": 328, "message": "可能的空值引用: escrow", "severity": "medium", "code": "{escrow.txHash && ("}, {"type": "potential-null-reference", "file": "app\\admin\\escrow\\page.tsx", "line": 330, "message": "可能的空值引用: escrow", "severity": "medium", "code": "<span className=\"text-xs font-mono block mt-1 break-all\">{escrow.txHash}</span>"}, {"type": "potential-null-reference", "file": "app\\admin\\escrow\\page.tsx", "line": 340, "message": "可能的空值引用: escrow", "severity": "medium", "code": "<div><span className=\"font-medium\">订单号:</span> {escrow.order.orderNumber}</div>"}, {"type": "potential-null-reference", "file": "app\\admin\\escrow\\page.tsx", "line": 341, "message": "可能的空值引用: escrow", "severity": "medium", "code": "<div><span className=\"font-medium\">商品:</span> {escrow.order.product.title}</div>"}, {"type": "potential-null-reference", "file": "app\\admin\\escrow\\page.tsx", "line": 342, "message": "可能的空值引用: escrow", "severity": "medium", "code": "<div><span className=\"font-medium\">商品价格:</span> {formatCurrency(escrow.order.product.price)}</div>"}, {"type": "potential-null-reference", "file": "app\\admin\\escrow\\page.tsx", "line": 345, "message": "可能的空值引用: escrow", "severity": "medium", "code": "{escrow.order.status}"}, {"type": "potential-null-reference", "file": "app\\admin\\escrow\\page.tsx", "line": 358, "message": "可能的空值引用: escrow", "severity": "medium", "code": "{escrow.order.buyer.name || '未设置'} ({escrow.order.buyer.email})"}, {"type": "potential-null-reference", "file": "app\\admin\\escrow\\page.tsx", "line": 361, "message": "可能的空值引用: escrow", "severity": "medium", "code": "信用分: {escrow.order.buyer.creditScore}"}, {"type": "potential-null-reference", "file": "app\\admin\\escrow\\page.tsx", "line": 367, "message": "可能的空值引用: escrow", "severity": "medium", "code": "{escrow.order.seller.name || '未设置'} ({escrow.order.seller.email})"}, {"type": "potential-null-reference", "file": "app\\admin\\escrow\\page.tsx", "line": 370, "message": "可能的空值引用: escrow", "severity": "medium", "code": "信用分: {escrow.order.seller.creditScore}"}, {"type": "potential-null-reference", "file": "app\\admin\\escrow\\page.tsx", "line": 380, "message": "可能的空值引用: escrow", "severity": "medium", "code": "<span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusBadgeColor(escrow.status)}`}>"}, {"type": "potential-null-reference", "file": "app\\admin\\escrow\\page.tsx", "line": 381, "message": "可能的空值引用: escrow", "severity": "medium", "code": "{getStatusText(escrow.status)}"}, {"type": "potential-null-reference", "file": "app\\admin\\escrow\\page.tsx", "line": 386, "message": "可能的空值引用: escrow", "severity": "medium", "code": "{escrow.status === 'PENDING' && ("}, {"type": "potential-null-reference", "file": "app\\admin\\escrow\\page.tsx", "line": 388, "message": "可能的空值引用: escrow", "severity": "medium", "code": "onClick={() => handleEscrowAction(escrow.id, 'confirmFunding', { notes: '管理员确认资金到账' })}"}, {"type": "potential-null-reference", "file": "app\\admin\\escrow\\page.tsx", "line": 389, "message": "可能的空值引用: escrow", "severity": "medium", "code": "disabled={actionLoading === escrow.id}"}, {"type": "potential-null-reference", "file": "app\\admin\\escrow\\page.tsx", "line": 392, "message": "可能的空值引用: escrow", "severity": "medium", "code": "{actionLoading === escrow.id ? '处理中...' : '确认到账'}"}, {"type": "potential-null-reference", "file": "app\\admin\\escrow\\page.tsx", "line": 396, "message": "可能的空值引用: escrow", "severity": "medium", "code": "{escrow.status === 'FUNDED' && ("}, {"type": "potential-null-reference", "file": "app\\admin\\escrow\\page.tsx", "line": 399, "message": "可能的空值引用: escrow", "severity": "medium", "code": "onClick={() => handleEscrowAction(escrow.id, 'releaseFunds', { notes: '管理员释放资金给卖家' })}"}, {"type": "potential-null-reference", "file": "app\\admin\\escrow\\page.tsx", "line": 400, "message": "可能的空值引用: escrow", "severity": "medium", "code": "disabled={actionLoading === escrow.id}"}, {"type": "potential-null-reference", "file": "app\\admin\\escrow\\page.tsx", "line": 403, "message": "可能的空值引用: escrow", "severity": "medium", "code": "{actionLoading === escrow.id ? '处理中...' : '释放资金'}"}, {"type": "potential-null-reference", "file": "app\\admin\\escrow\\page.tsx", "line": 407, "message": "可能的空值引用: escrow", "severity": "medium", "code": "const refundAmount = prompt('请输入退款金额 (USDT):', escrow.amount.toString())"}, {"type": "potential-null-reference", "file": "app\\admin\\escrow\\page.tsx", "line": 409, "message": "可能的空值引用: escrow", "severity": "medium", "code": "handleEscrowAction(escrow.id, 'refundBuyer', {"}, {"type": "potential-null-reference", "file": "app\\admin\\escrow\\page.tsx", "line": 415, "message": "可能的空值引用: escrow", "severity": "medium", "code": "disabled={actionLoading === escrow.id}"}, {"type": "potential-null-reference", "file": "app\\admin\\escrow\\page.tsx", "line": 418, "message": "可能的空值引用: escrow", "severity": "medium", "code": "{actionLoading === escrow.id ? '处理中...' : '退款买家'}"}, {"type": "potential-null-reference", "file": "app\\admin\\escrow\\page.tsx", "line": 425, "message": "可能的空值引用: escrow", "severity": "medium", "code": "const notes = prompt('请输入管理员备注:', escrow.adminNotes || '')"}, {"type": "potential-null-reference", "file": "app\\admin\\escrow\\page.tsx", "line": 427, "message": "可能的空值引用: escrow", "severity": "medium", "code": "handleEscrowAction(escrow.id, 'addNotes', { notes })"}, {"type": "potential-null-reference", "file": "app\\admin\\escrow\\page.tsx", "line": 438, "message": "可能的空值引用: escrow", "severity": "medium", "code": "{escrow.fundedAt && ("}, {"type": "potential-null-reference", "file": "app\\admin\\escrow\\page.tsx", "line": 439, "message": "可能的空值引用: escrow", "severity": "medium", "code": "<div>到账时间: {formatDate(escrow.fundedAt.toString())}</div>"}, {"type": "potential-null-reference", "file": "app\\admin\\escrow\\page.tsx", "line": 441, "message": "可能的空值引用: escrow", "severity": "medium", "code": "{escrow.releasedAt && ("}, {"type": "potential-null-reference", "file": "app\\admin\\escrow\\page.tsx", "line": 442, "message": "可能的空值引用: escrow", "severity": "medium", "code": "<div>释放时间: {formatDate(escrow.releasedAt.toString())}</div>"}, {"type": "potential-null-reference", "file": "app\\admin\\escrow\\page.tsx", "line": 444, "message": "可能的空值引用: escrow", "severity": "medium", "code": "{escrow.refundedAt && ("}, {"type": "potential-null-reference", "file": "app\\admin\\escrow\\page.tsx", "line": 445, "message": "可能的空值引用: escrow", "severity": "medium", "code": "<div>退款时间: {formatDate(escrow.refundedAt.toString())}</div>"}, {"type": "potential-null-reference", "file": "app\\admin\\escrow\\page.tsx", "line": 450, "message": "可能的空值引用: escrow", "severity": "medium", "code": "{escrow.adminNotes && ("}, {"type": "potential-null-reference", "file": "app\\admin\\escrow\\page.tsx", "line": 452, "message": "可能的空值引用: escrow", "severity": "medium", "code": "<strong>备注:</strong> {escrow.adminNotes}"}, {"type": "potential-null-reference", "file": "app\\admin\\help\\media\\page.tsx", "line": 123, "message": "可能的空值引用: data", "severity": "medium", "code": "setFiles(data.files)"}, {"type": "potential-null-reference", "file": "app\\admin\\help\\media\\page.tsx", "line": 124, "message": "可能的空值引用: data", "severity": "medium", "code": "setPagination(data.pagination)"}, {"type": "potential-null-reference", "file": "app\\admin\\help\\media\\page.tsx", "line": 143, "message": "可能的空值引用: data", "severity": "medium", "code": "setStats(data.statistics)"}, {"type": "potential-null-reference", "file": "app\\admin\\help\\media\\page.tsx", "line": 167, "message": "可能的空值引用: data", "severity": "medium", "code": "alert(data.error || '删除失败')"}, {"type": "unhandled-promise", "file": "app\\admin\\help\\media\\page.tsx", "line": 191, "message": "await调用缺少错误处理", "severity": "medium", "code": "const data = await response.json()"}, {"type": "potential-null-reference", "file": "app\\admin\\help\\media\\page.tsx", "line": 192, "message": "可能的空值引用: data", "severity": "medium", "code": "setCleanupResults(data.results)"}, {"type": "potential-null-reference", "file": "app\\admin\\help\\media\\page.tsx", "line": 195, "message": "可能的空值引用: data", "severity": "medium", "code": "alert(`清理完成！删除了 ${data.results.summary.totalDeleted} 个文件`)"}, {"type": "potential-null-reference", "file": "app\\admin\\help\\media\\page.tsx", "line": 201, "message": "可能的空值引用: data", "severity": "medium", "code": "alert(data.error || '清理失败')"}, {"type": "unsafe-array-access", "file": "app\\admin\\help\\media\\page.tsx", "line": 231, "message": "不安全的数组访问: sizes[i]", "severity": "low", "code": "return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]"}, {"type": "potential-null-reference", "file": "app\\admin\\help\\media\\page.tsx", "line": 363, "message": "可能的空值引用: e", "severity": "medium", "code": "onChange={(e) => setTypeFilter(e.target.value)}"}, {"type": "potential-null-reference", "file": "app\\admin\\help\\media\\page.tsx", "line": 379, "message": "可能的空值引用: e", "severity": "medium", "code": "onChange={(e) => setStatusFilter(e.target.value)}"}, {"type": "potential-null-reference", "file": "app\\admin\\help\\media\\page.tsx", "line": 399, "message": "可能的空值引用: e", "severity": "medium", "code": "onChange={(e) => setSearchQuery(e.target.value)}"}, {"type": "potential-null-reference", "file": "app\\admin\\help\\media\\page.tsx", "line": 400, "message": "可能的空值引用: e", "severity": "medium", "code": "onKeyPress={(e) => e.key === 'Enter' && handleSearch()}"}], "security": [], "performance": []}}