
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BitMarket 测试报告</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 8px 8px 0 0; }
        .header h1 { margin: 0; font-size: 2.5em; }
        .header p { margin: 10px 0 0 0; opacity: 0.9; }
        .summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; padding: 30px; }
        .metric { text-align: center; padding: 20px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #28a745; }
        .metric.failed { border-left-color: #dc3545; }
        .metric h3 { margin: 0; font-size: 2em; color: #333; }
        .metric p { margin: 5px 0 0 0; color: #666; }
        .section { padding: 30px; border-top: 1px solid #eee; }
        .section h2 { color: #333; margin-bottom: 20px; }
        .test-suite { background: #f8f9fa; border-radius: 8px; padding: 20px; margin-bottom: 20px; }
        .test-suite h3 { color: #495057; margin-top: 0; }
        .test-details { list-style: none; padding: 0; }
        .test-details li { padding: 5px 0; color: #28a745; }
        .performance-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
        .performance-card { background: #f8f9fa; border-radius: 8px; padding: 20px; }
        .performance-card h4 { margin-top: 0; color: #495057; }
        .performance-item { display: flex; justify-content: space-between; padding: 8px 0; border-bottom: 1px solid #dee2e6; }
        .performance-item:last-child { border-bottom: none; }
        .footer { background: #f8f9fa; padding: 20px 30px; border-radius: 0 0 8px 8px; color: #666; font-size: 0.9em; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 BitMarket 测试报告</h1>
            <p>生成时间: 2025-07-18T12:14:10.418Z</p>
        </div>
        
        <div class="summary">
            <div class="metric">
                <h3>42</h3>
                <p>总测试数</p>
            </div>
            <div class="metric">
                <h3>42</h3>
                <p>通过测试</p>
            </div>
            <div class="metric ">
                <h3>0</h3>
                <p>失败测试</p>
            </div>
            <div class="metric">
                <h3>100%</h3>
                <p>成功率</p>
            </div>
        </div>
        
        <div class="section">
            <h2>📋 测试套件详情</h2>
            
                <div class="test-suite">
                    <h3>基础测试 (test/basic.test.ts)</h3>
                    <p><strong>测试数:</strong> 5 | <strong>通过:</strong> 5 | <strong>失败:</strong> 0 | <strong>耗时:</strong> 16ms</p>
                    <ul class="test-details">
                        <li>✅ 应该能够运行基本测试</li><li>✅ 应该能够测试字符串</li><li>✅ 应该能够测试对象</li><li>✅ 应该能够测试数组</li><li>✅ 应该能够测试异步函数</li>
                    </ul>
                </div>
            
                <div class="test-suite">
                    <h3>数据工厂测试 (test/factories/factory.test.ts)</h3>
                    <p><strong>测试数:</strong> 26 | <strong>通过:</strong> 26 | <strong>失败:</strong> 0 | <strong>耗时:</strong> 77ms</p>
                    <ul class="test-details">
                        <li>✅ UserFactory - 用户数据生成 (5个测试)</li><li>✅ ProductFactory - 商品数据生成 (3个测试)</li><li>✅ OrderFactory - 订单数据生成 (4个测试)</li><li>✅ MessageFactory - 消息数据生成 (3个测试)</li><li>✅ ReviewFactory - 评价数据生成 (3个测试)</li><li>✅ FeedbackFactory - 反馈数据生成 (3个测试)</li><li>✅ DataGenerator - 综合数据生成 (2个测试)</li><li>✅ 数据一致性测试 (3个测试)</li>
                    </ul>
                </div>
            
                <div class="test-suite">
                    <h3>性能测试 (test/performance/simple-performance.test.ts)</h3>
                    <p><strong>测试数:</strong> 11 | <strong>通过:</strong> 11 | <strong>失败:</strong> 0 | <strong>耗时:</strong> 1726ms</p>
                    <ul class="test-details">
                        <li>✅ 数据生成性能测试 (3个测试)</li><li>✅ 并发操作模拟 (2个测试)</li><li>✅ 内存使用测试 (1个测试)</li><li>✅ 算法性能测试 (3个测试)</li><li>✅ 缓存性能模拟 (2个测试)</li>
                    </ul>
                </div>
            
        </div>
        
        <div class="section">
            <h2>⚡ 性能指标</h2>
            <div class="performance-grid">
                <div class="performance-card">
                    <h4>数据生成性能</h4>
                    
                        <div class="performance-item">
                            <span>1000个用户</span>
                            <span>138.14ms</span>
                        </div>
                    
                        <div class="performance-item">
                            <span>500个商品</span>
                            <span>40.73ms</span>
                        </div>
                    
                        <div class="performance-item">
                            <span>200个订单</span>
                            <span>32.95ms</span>
                        </div>
                    
                </div>
                <div class="performance-card">
                    <h4>并发性能</h4>
                    
                        <div class="performance-item">
                            <span>50个并发查询</span>
                            <span>20.82ms</span>
                        </div>
                    
                        <div class="performance-item">
                            <span>100个API调用</span>
                            <span>0.22ms</span>
                        </div>
                    
                </div>
                <div class="performance-card">
                    <h4>算法性能</h4>
                    
                        <div class="performance-item">
                            <span>搜索10000个商品</span>
                            <span>8.89ms</span>
                        </div>
                    
                        <div class="performance-item">
                            <span>排序5000个商品</span>
                            <span>3.87ms</span>
                        </div>
                    
                        <div class="performance-item">
                            <span>分页处理</span>
                            <span>0.00ms</span>
                        </div>
                    
                </div>
                <div class="performance-card">
                    <h4>缓存性能</h4>
                    
                        <div class="performance-item">
                            <span>100次缓存查询</span>
                            <span>0.05ms</span>
                        </div>
                    
                        <div class="performance-item">
                            <span>10次缓存未命中</span>
                            <span>0.34ms</span>
                        </div>
                    
                </div>
            </div>
        </div>
        
        <div class="footer">
            <p><strong>测试环境:</strong> Vitest 3.2.4 on v22.17.0 (win32 x64)</p>
        </div>
    </div>
</body>
</html>
  