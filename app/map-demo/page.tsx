'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  MapPin, 
  Navigation, 
  Target,
  Info,
  Settings,
  Zap,
  Globe
} from 'lucide-react'

import MapView from '@/components/location/MapView'
import AMapView from '@/components/location/AMapView'
import NearbySearch from '@/components/location/NearbySearch'
import LocationPicker from '@/components/location/LocationPicker'

export default function MapDemoPage() {
  const [currentLocation, setCurrentLocation] = useState<{
    latitude: number
    longitude: number
  } | null>(null)
  const [searchResults, setSearchResults] = useState<any>(null)
  const [selectedLocation, setSelectedLocation] = useState({
    latitude: 39.9042,
    longitude: 116.4074,
    address: '北京市朝阳区',
    city: '北京市',
    district: '朝阳区'
  })

  // 模拟搜索结果数据
  const mockSearchResults = {
    products: [
      {
        item: {
          id: '1',
          title: 'iPhone 15 Pro Max',
          price: 8999,
          latitude: 39.9142,
          longitude: 116.4174,
          seller: { name: '数码达人' }
        },
        distance: 1.2,
        distanceText: '1.2km'
      },
      {
        item: {
          id: '2',
          title: 'MacBook Pro M3',
          price: 15999,
          latitude: 39.8942,
          longitude: 116.3974,
          seller: { name: '科技爱好者' }
        },
        distance: 2.8,
        distanceText: '2.8km'
      },
      {
        item: {
          id: '3',
          title: 'AirPods Pro 2',
          price: 1899,
          latitude: 39.9242,
          longitude: 116.4274,
          seller: { name: '音频专家' }
        },
        distance: 3.5,
        distanceText: '3.5km'
      }
    ],
    total: 3,
    hasMore: false,
    metadata: {
      searchCenter: selectedLocation,
      searchRadius: 5,
      searchRadiusText: '5km'
    }
  }

  useEffect(() => {
    // 获取用户当前位置
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          setCurrentLocation({
            latitude: position.coords.latitude,
            longitude: position.coords.longitude
          })
        },
        (error) => {
          console.log('获取位置失败:', error)
          // 使用默认位置（北京）
          setCurrentLocation({
            latitude: 39.9042,
            longitude: 116.4074
          })
        }
      )
    }
  }, [])

  const handleSearchResults = (results: any) => {
    setSearchResults(results)
  }

  const handleLocationChange = (location: any) => {
    setSelectedLocation(location)
  }

  const handleItemClick = (item: any) => {
    console.log('点击地图项目:', item)
    alert(`查看商品: ${item.title}`)
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* 页面标题 */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">🗺️ 地图集成演示</h1>
        <p className="text-gray-600">
          展示BitMarket平台的地理位置功能，包括附近搜索、地图视图和位置管理
        </p>
      </div>

      {/* 功能概览 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
        <Card>
          <CardContent className="p-4 text-center">
            <Target className="h-8 w-8 text-blue-500 mx-auto mb-2" />
            <h3 className="font-medium">精准定位</h3>
            <p className="text-sm text-gray-600">GPS自动定位</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <Navigation className="h-8 w-8 text-green-500 mx-auto mb-2" />
            <h3 className="font-medium">附近搜索</h3>
            <p className="text-sm text-gray-600">1-50km可调半径</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <Globe className="h-8 w-8 text-purple-500 mx-auto mb-2" />
            <h3 className="font-medium">地图视图</h3>
            <p className="text-sm text-gray-600">高德地图集成</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <Zap className="h-8 w-8 text-orange-500 mx-auto mb-2" />
            <h3 className="font-medium">实时更新</h3>
            <p className="text-sm text-gray-600">动态距离计算</p>
          </CardContent>
        </Card>
      </div>

      {/* 主要功能演示 */}
      <Tabs defaultValue="search" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="search">附近搜索</TabsTrigger>
          <TabsTrigger value="map">地图视图</TabsTrigger>
          <TabsTrigger value="location">位置设置</TabsTrigger>
          <TabsTrigger value="integration">API集成</TabsTrigger>
        </TabsList>

        {/* 附近搜索演示 */}
        <TabsContent value="search" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Navigation className="h-5 w-5" />
                <span>附近搜索功能</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <NearbySearch
                type="product"
                onResults={handleSearchResults}
                initialLocation={currentLocation || undefined}
              />
            </CardContent>
          </Card>

          {/* 搜索结果展示 */}
          {(searchResults || mockSearchResults) && (
            <Card>
              <CardHeader>
                <CardTitle>搜索结果</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {(searchResults?.products || mockSearchResults.products).map((result: any) => (
                    <Card key={result.item.id} className="cursor-pointer hover:shadow-md transition-shadow">
                      <CardContent className="p-4">
                        <div className="flex items-start justify-between mb-2">
                          <h4 className="font-medium text-sm">{result.item.title}</h4>
                          <Badge variant="outline">{result.distanceText}</Badge>
                        </div>
                        <p className="text-lg font-bold text-blue-600 mb-1">
                          ¥{result.item.price.toFixed(2)}
                        </p>
                        <p className="text-xs text-gray-500">
                          卖家: {result.item.seller.name}
                        </p>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* 地图视图演示 */}
        <TabsContent value="map" className="space-y-6">
          <Alert>
            <Info className="h-4 w-4" />
            <AlertDescription>
              地图视图支持两种模式：模拟地图（无需API密钥）和高德地图（需要API密钥）
            </AlertDescription>
          </Alert>

          <Tabs defaultValue="mock" className="space-y-4">
            <TabsList>
              <TabsTrigger value="mock">模拟地图</TabsTrigger>
              <TabsTrigger value="amap">高德地图</TabsTrigger>
            </TabsList>

            <TabsContent value="mock">
              <MapView
                center={selectedLocation}
                items={mockSearchResults.products.map(result => ({
                  id: result.item.id,
                  title: result.item.title,
                  latitude: result.item.latitude,
                  longitude: result.item.longitude,
                  price: result.item.price,
                  type: 'product' as const,
                  distance: result.distance,
                  distanceText: result.distanceText,
                  item: result.item
                }))}
                searchRadius={5}
                onItemClick={handleItemClick}
              />
            </TabsContent>

            <TabsContent value="amap">
              <AMapView
                center={selectedLocation}
                items={mockSearchResults.products.map(result => ({
                  id: result.item.id,
                  title: result.item.title,
                  latitude: result.item.latitude,
                  longitude: result.item.longitude,
                  price: result.item.price,
                  type: 'product' as const,
                  distance: result.distance,
                  distanceText: result.distanceText,
                  item: result.item
                }))}
                searchRadius={5}
                onItemClick={handleItemClick}
              />
            </TabsContent>
          </Tabs>
        </TabsContent>

        {/* 位置设置演示 */}
        <TabsContent value="location" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Settings className="h-5 w-5" />
                <span>位置设置功能</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <LocationPicker
                initialLocation={selectedLocation}
                onLocationChange={handleLocationChange}
                showPublicOption={true}
                showRadiusOption={true}
                showLocalTradeOption={true}
              />
            </CardContent>
          </Card>

          {/* 位置信息展示 */}
          <Card>
            <CardHeader>
              <CardTitle>当前位置信息</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-gray-600">地址:</span>
                  <span>{selectedLocation.address}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">城市:</span>
                  <span>{selectedLocation.city} {selectedLocation.district}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">坐标:</span>
                  <span>{selectedLocation.latitude.toFixed(4)}, {selectedLocation.longitude.toFixed(4)}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* API集成说明 */}
        <TabsContent value="integration" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>API接口说明</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h4 className="font-medium mb-2">附近搜索API</h4>
                <div className="bg-gray-100 p-3 rounded text-sm font-mono">
                  GET /api/location/nearby?type=product&lat=39.9042&lng=116.4074&radius=5
                </div>
              </div>
              
              <div>
                <h4 className="font-medium mb-2">位置管理API</h4>
                <div className="bg-gray-100 p-3 rounded text-sm font-mono">
                  PUT /api/location/manage
                </div>
              </div>

              <div>
                <h4 className="font-medium mb-2">距离计算API</h4>
                <div className="bg-gray-100 p-3 rounded text-sm font-mono">
                  POST /api/location/nearby
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>环境变量配置</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="bg-gray-100 p-3 rounded text-sm font-mono">
                  NEXT_PUBLIC_AMAP_API_KEY=your-amap-api-key
                </div>
                <div className="bg-gray-100 p-3 rounded text-sm font-mono">
                  NEXT_PUBLIC_ENABLE_GEOLOCATION=true
                </div>
                <div className="bg-gray-100 p-3 rounded text-sm font-mono">
                  NEXT_PUBLIC_ENABLE_MAP_VIEW=true
                </div>
              </div>
              <p className="text-sm text-gray-600 mt-2">
                详细配置说明请参考 <code>docs/map-integration-guide.md</code>
              </p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* 功能特性说明 */}
      <Card className="mt-8">
        <CardHeader>
          <CardTitle>🎯 功能特性</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium mb-2">🔍 搜索功能</h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• 1-50km可调搜索半径</li>
                <li>• 实时距离计算和排序</li>
                <li>• 多维度筛选（分类、价格、时间）</li>
                <li>• 边界框优化查询性能</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium mb-2">🗺️ 地图功能</h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• 高德地图API集成</li>
                <li>• 交互式标记和信息窗口</li>
                <li>• 搜索范围可视化</li>
                <li>• 缩放、平移、重新定位</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium mb-2">📍 位置管理</h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• GPS自动定位</li>
                <li>• 手动地址搜索</li>
                <li>• 隐私控制设置</li>
                <li>• 交易半径配置</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium mb-2">🔒 隐私保护</h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• 位置脱敏显示</li>
                <li>• 用户可控的公开设置</li>
                <li>• 数据加密存储</li>
                <li>• 合规的隐私政策</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
