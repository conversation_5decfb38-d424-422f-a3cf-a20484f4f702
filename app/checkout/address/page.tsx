'use client'

import { useState, useEffect } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { useSession } from 'next-auth/react'

interface Address {
  id: string
  name: string
  phone: string
  province: string
  city: string
  district: string
  detail: string
  isDefault: boolean
}

export default function AddressSelectionPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const { data: session } = useSession()
  
  const [addresses, setAddresses] = useState<Address[]>([])
  const [selectedAddressId, setSelectedAddressId] = useState<string>('')
  const [showAddForm, setShowAddForm] = useState(false)
  const [loading, setLoading] = useState(true)
  const [submitting, setSubmitting] = useState(false)

  // 编辑地址相关状态
  const [editingAddressId, setEditingAddressId] = useState<string>('')
  const [editingAddress, setEditingAddress] = useState<Address | null>(null)

  // 新地址表单数据
  const [newAddress, setNewAddress] = useState({
    name: '',
    phone: '',
    province: '',
    city: '',
    district: '',
    detail: '',
    isDefault: false
  })

  // 从URL参数获取商品信息
  const productId = searchParams.get('productId')
  const variantId = searchParams.get('variantId')
  const quantity = parseInt(searchParams.get('quantity') || '1')
  const useEscrow = searchParams.get('useEscrow') === 'true'

  useEffect(() => {
    if (session?.user?.id) {
      fetchAddresses()
    }
  }, [session])

  const fetchAddresses = async () => {
    try {
      const response = await fetch('/api/addresses', {
        credentials: 'include'
      })
      
      if (response.ok) {
        const data = await response.json()
        setAddresses(data.data || [])
        
        // 自动选择默认地址
        const defaultAddress = data.data?.find((addr: Address) => addr.isDefault)
        if (defaultAddress) {
          setSelectedAddressId(defaultAddress.id)
        }
      }
    } catch (error) {
      console.error('获取地址列表失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleAddAddress = async (e: React.FormEvent) => {
    e.preventDefault()
    setSubmitting(true)

    try {
      const response = await fetch('/api/addresses', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        credentials: 'include',
        body: JSON.stringify(newAddress)
      })

      if (response.ok) {
        const data = await response.json()

        // 如果新地址设置为默认地址，需要更新所有地址的默认状态
        if (data.data.isDefault) {
          setAddresses(prev => [
            data.data,
            ...prev.map(addr => ({ ...addr, isDefault: false }))
          ])
        } else {
          setAddresses(prev => [data.data, ...prev])
        }

        setSelectedAddressId(data.data.id)
        setShowAddForm(false)
        setNewAddress({
          name: '',
          phone: '',
          province: '',
          city: '',
          district: '',
          detail: '',
          isDefault: false
        })
      } else {
        const error = await response.json()
        alert(error.error || '添加地址失败')
      }
    } catch (error) {
      console.error('添加地址失败:', error)
      alert('网络错误，请稍后重试')
    } finally {
      setSubmitting(false)
    }
  }

  const handleEditAddress = (address: Address) => {
    setEditingAddressId(address.id)
    setEditingAddress({ ...address })
    setShowAddForm(false) // 关闭添加表单
  }

  const handleUpdateAddress = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!editingAddress) return

    setSubmitting(true)

    try {
      const response = await fetch(`/api/addresses/${editingAddress.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        credentials: 'include',
        body: JSON.stringify({
          name: editingAddress.name,
          phone: editingAddress.phone,
          province: editingAddress.province,
          city: editingAddress.city,
          district: editingAddress.district,
          detail: editingAddress.detail,
          isDefault: editingAddress.isDefault
        })
      })

      if (response.ok) {
        const data = await response.json()

        // 如果设置了默认地址，需要更新所有地址的默认状态
        if (data.data.isDefault) {
          setAddresses(prev => prev.map(addr => ({
            ...addr,
            isDefault: addr.id === editingAddress.id ? true : false
          })))
        } else {
          // 如果没有设置默认地址，只更新当前地址
          setAddresses(prev => prev.map(addr =>
            addr.id === editingAddress.id ? data.data : addr
          ))
        }

        setEditingAddressId('')
        setEditingAddress(null)
      } else {
        const error = await response.json()
        alert(error.error || '更新地址失败')
      }
    } catch (error) {
      console.error('更新地址失败:', error)
      alert('网络错误，请稍后重试')
    } finally {
      setSubmitting(false)
    }
  }

  const handleCancelEdit = () => {
    setEditingAddressId('')
    setEditingAddress(null)
  }

  const handleConfirmAddress = () => {
    if (!selectedAddressId) {
      alert('请选择收货地址')
      return
    }

    const selectedAddress = addresses.find(addr => addr.id === selectedAddressId)
    if (!selectedAddress) {
      alert('选择的地址无效')
      return
    }

    // 构建订单创建参数
    const orderParams = new URLSearchParams({
      productId: productId || '',
      quantity: quantity.toString(),
      useEscrow: useEscrow.toString(),
      addressId: selectedAddressId
    })

    if (variantId) {
      orderParams.append('variantId', variantId)
    }

    // 跳转到订单确认页面
    router.push(`/checkout/confirm?${orderParams.toString()}`)
  }

  if (!session) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-4">请先登录</h2>
          <button
            onClick={() => router.push('/auth/signin')}
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md"
          >
            去登录
          </button>
        </div>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-lg">加载中...</div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-2xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow-md p-6">
          <h1 className="text-2xl font-bold mb-6">选择收货地址</h1>

          {/* 地址列表 */}
          <div className="space-y-4 mb-6">
            {addresses.map((address) => (
              <div key={address.id}>
                {editingAddressId === address.id ? (
                  // 编辑表单
                  <div className="border rounded-lg p-4 bg-yellow-50 border-yellow-200">
                    <h3 className="font-medium mb-4 text-yellow-800">编辑地址</h3>
                    <form onSubmit={handleUpdateAddress} className="space-y-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            收件人姓名 *
                          </label>
                          <input
                            type="text"
                            required
                            value={editingAddress?.name || ''}
                            onChange={(e) => setEditingAddress(prev => prev ? { ...prev, name: e.target.value } : null)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            placeholder="请输入收件人姓名"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            手机号码 *
                          </label>
                          <input
                            type="tel"
                            required
                            value={editingAddress?.phone || ''}
                            onChange={(e) => setEditingAddress(prev => prev ? { ...prev, phone: e.target.value } : null)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            placeholder="请输入手机号码"
                          />
                        </div>
                      </div>

                      <div className="grid grid-cols-3 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            省份 *
                          </label>
                          <input
                            type="text"
                            required
                            value={editingAddress?.province || ''}
                            onChange={(e) => setEditingAddress(prev => prev ? { ...prev, province: e.target.value } : null)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            placeholder="省份"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            城市 *
                          </label>
                          <input
                            type="text"
                            required
                            value={editingAddress?.city || ''}
                            onChange={(e) => setEditingAddress(prev => prev ? { ...prev, city: e.target.value } : null)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            placeholder="城市"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            区县 *
                          </label>
                          <input
                            type="text"
                            required
                            value={editingAddress?.district || ''}
                            onChange={(e) => setEditingAddress(prev => prev ? { ...prev, district: e.target.value } : null)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            placeholder="区县"
                          />
                        </div>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          详细地址 *
                        </label>
                        <textarea
                          required
                          value={editingAddress?.detail || ''}
                          onChange={(e) => setEditingAddress(prev => prev ? { ...prev, detail: e.target.value } : null)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          rows={3}
                          placeholder="请输入详细地址，如街道、门牌号等"
                        />
                      </div>

                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          id={`editDefault-${address.id}`}
                          checked={editingAddress?.isDefault || false}
                          onChange={(e) => setEditingAddress(prev => prev ? { ...prev, isDefault: e.target.checked } : null)}
                          className="w-4 h-4 text-blue-600 rounded"
                        />
                        <label htmlFor={`editDefault-${address.id}`} className="ml-2 text-sm text-gray-700">
                          设为默认地址
                        </label>
                      </div>

                      <div className="flex gap-3">
                        <button
                          type="submit"
                          disabled={submitting}
                          className="flex-1 bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md disabled:opacity-50"
                        >
                          {submitting ? '保存中...' : '保存修改'}
                        </button>
                        <button
                          type="button"
                          onClick={handleCancelEdit}
                          className="flex-1 bg-gray-300 hover:bg-gray-400 text-gray-700 py-2 px-4 rounded-md"
                        >
                          取消
                        </button>
                      </div>
                    </form>
                  </div>
                ) : (
                  // 地址显示
                  <div
                    className={`border rounded-lg p-4 transition-colors ${
                      selectedAddressId === address.id
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <div className="flex items-start justify-between">
                      <div
                        className="flex-1 cursor-pointer"
                        onClick={() => setSelectedAddressId(address.id)}
                      >
                        <div className="flex items-center gap-2 mb-2">
                          <span className="font-medium">{address.name}</span>
                          <span className="text-gray-600">{address.phone}</span>
                          {address.isDefault && (
                            <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">
                              默认
                            </span>
                          )}
                        </div>
                        <div className="text-gray-700">
                          {address.province} {address.city} {address.district} {address.detail}
                        </div>
                      </div>
                      <div className="ml-4 flex items-center gap-2">
                        <button
                          onClick={(e) => {
                            e.stopPropagation()
                            handleEditAddress(address)
                          }}
                          className="text-blue-600 hover:text-blue-800 text-sm px-2 py-1 rounded hover:bg-blue-50 transition-colors"
                        >
                          编辑
                        </button>
                        <input
                          type="radio"
                          name="address"
                          checked={selectedAddressId === address.id}
                          onChange={() => setSelectedAddressId(address.id)}
                          className="w-4 h-4 text-blue-600"
                        />
                      </div>
                    </div>
                  </div>
                )}
              </div>
            ))}

            {addresses.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                暂无收货地址，请添加新地址
              </div>
            )}
          </div>

          {/* 添加新地址按钮 */}
          {!showAddForm && !editingAddressId && (
            <button
              onClick={() => setShowAddForm(true)}
              className="w-full border-2 border-dashed border-gray-300 rounded-lg py-4 text-gray-600 hover:border-gray-400 hover:text-gray-700 transition-colors"
            >
              + 添加新地址
            </button>
          )}

          {/* 添加地址表单 */}
          {showAddForm && !editingAddressId && (
            <div className="border rounded-lg p-4 bg-gray-50">
              <h3 className="font-medium mb-4">添加新地址</h3>
              <form onSubmit={handleAddAddress} className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      收件人姓名 *
                    </label>
                    <input
                      type="text"
                      required
                      value={newAddress.name}
                      onChange={(e) => setNewAddress(prev => ({ ...prev, name: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="请输入收件人姓名"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      手机号码 *
                    </label>
                    <input
                      type="tel"
                      required
                      value={newAddress.phone}
                      onChange={(e) => setNewAddress(prev => ({ ...prev, phone: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="请输入手机号码"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      省份 *
                    </label>
                    <input
                      type="text"
                      required
                      value={newAddress.province}
                      onChange={(e) => setNewAddress(prev => ({ ...prev, province: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="省份"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      城市 *
                    </label>
                    <input
                      type="text"
                      required
                      value={newAddress.city}
                      onChange={(e) => setNewAddress(prev => ({ ...prev, city: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="城市"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      区县 *
                    </label>
                    <input
                      type="text"
                      required
                      value={newAddress.district}
                      onChange={(e) => setNewAddress(prev => ({ ...prev, district: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="区县"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    详细地址 *
                  </label>
                  <textarea
                    required
                    value={newAddress.detail}
                    onChange={(e) => setNewAddress(prev => ({ ...prev, detail: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    rows={3}
                    placeholder="请输入详细地址，如街道、门牌号等"
                  />
                </div>

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="isDefault"
                    checked={newAddress.isDefault}
                    onChange={(e) => setNewAddress(prev => ({ ...prev, isDefault: e.target.checked }))}
                    className="w-4 h-4 text-blue-600 rounded"
                  />
                  <label htmlFor="isDefault" className="ml-2 text-sm text-gray-700">
                    设为默认地址
                  </label>
                </div>

                <div className="flex gap-3">
                  <button
                    type="submit"
                    disabled={submitting}
                    className="flex-1 bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md disabled:opacity-50"
                  >
                    {submitting ? '保存中...' : '保存地址'}
                  </button>
                  <button
                    type="button"
                    onClick={() => setShowAddForm(false)}
                    className="flex-1 bg-gray-300 hover:bg-gray-400 text-gray-700 py-2 px-4 rounded-md"
                  >
                    取消
                  </button>
                </div>
              </form>
            </div>
          )}

          {/* 确认按钮 */}
          <div className="mt-6 pt-6 border-t">
            <button
              onClick={handleConfirmAddress}
              disabled={!selectedAddressId}
              className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-300 text-white py-3 px-4 rounded-md font-medium"
            >
              确认地址，继续下单
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
