'use client'

import { useState, useEffect } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { useSession } from 'next-auth/react'
import Image from 'next/image'
import { formatUSDT } from '@/lib/utils'

interface Address {
  id: string
  name: string
  phone: string
  province: string
  city: string
  district: string
  detail: string
}

interface Product {
  id: string
  title: string
  price: number
  images: string[]
  seller: {
    name: string
  }
}

interface ProductVariant {
  id: string
  price: number
  attributes: Array<{ name: string; value: string }>
}

export default function OrderConfirmPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const { data: session } = useSession()
  
  const [product, setProduct] = useState<Product | null>(null)
  const [variant, setVariant] = useState<ProductVariant | null>(null)
  const [address, setAddress] = useState<Address | null>(null)
  const [autoAssignedMediator, setAutoAssignedMediator] = useState<any>(null)
  const [escrowFee, setEscrowFee] = useState(0)
  const [loading, setLoading] = useState(true)
  const [submitting, setSubmitting] = useState(false)

  // 从URL参数获取订单信息
  const productId = searchParams.get('productId')
  const variantId = searchParams.get('variantId')
  const quantity = parseInt(searchParams.get('quantity') || '1')
  const useEscrow = searchParams.get('useEscrow') === 'true'
  const addressId = searchParams.get('addressId')

  useEffect(() => {
    if (session?.user?.id && productId && addressId) {
      loadOrderData()
    }
  }, [session, productId, addressId])

  const loadOrderData = async () => {
    try {
      // 并行加载商品、地址和中间人信息
      const promises = [
        fetch(`/api/products/${productId}`, { credentials: 'include' }),
        fetch(`/api/addresses/${addressId}`, { credentials: 'include' })
      ]

      if (useEscrow) {
        const orderAmount = variant ? variant.price * quantity : (product?.price || 0) * quantity
        promises.push(
          fetch('/api/mediator/auto-assign', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            credentials: 'include',
            body: JSON.stringify({ orderAmount })
          })
        )
      }

      const responses = await Promise.all(promises)
      
      // 处理商品数据
      if (responses[0].ok) {
        const productData = await responses[0].json()
        setProduct(productData)
        
        if (variantId && productData.variants) {
          const selectedVariant = productData.variants.find((v: ProductVariant) => v.id === variantId)
          setVariant(selectedVariant || null)
        }
      }

      // 处理地址数据
      if (responses[1].ok) {
        const addressData = await responses[1].json()
        setAddress(addressData.data)
      }

      // 处理中间人数据
      if (useEscrow && responses[2] && responses[2].ok) {
        const mediatorData = await responses[2].json()
        if (mediatorData.success) {
          setAutoAssignedMediator(mediatorData.data)
          setEscrowFee(mediatorData.data.escrowFee)
        }
      }

    } catch (error) {
      console.error('加载订单数据失败:', error)
      alert('加载订单信息失败，请重试')
    } finally {
      setLoading(false)
    }
  }

  const handleCreateOrder = async () => {
    if (!product || !address) {
      alert('订单信息不完整')
      return
    }

    setSubmitting(true)

    try {
      console.log('🔄 开始创建订单...')
      
      // 创建订单
      const orderResponse = await fetch('/api/orders', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          productId: product.id,
          variantId: variant?.id,
          quantity,
          shippingAddress: {
            name: address.name,
            phone: address.phone,
            province: address.province,
            city: address.city,
            district: address.district,
            detail: address.detail
          }
        }),
      })

      console.log('订单创建响应状态:', orderResponse.status)

      if (!orderResponse.ok) {
        const data = await orderResponse.json()
        console.log('❌ 订单创建失败:', data)
        alert(data.error || '创建订单失败')
        return
      }

      const order = await orderResponse.json()
      console.log('✅ 订单创建成功:', order.id)

      // 如果使用托管，创建托管订单
      if (useEscrow && autoAssignedMediator) {
        console.log('🔄 开始创建托管订单...')
        
        try {
          const escrowResponse = await fetch('/api/escrow/create', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            credentials: 'include',
            body: JSON.stringify({
              orderId: order.id,
              mediatorId: autoAssignedMediator.mediator.id,
              escrowAmount: variant ? variant.price * quantity : product.price * quantity,
              escrowFee: escrowFee
            }),
          })

          console.log('托管订单创建响应状态:', escrowResponse.status)

          if (escrowResponse.ok) {
            const escrowData = await escrowResponse.json()
            console.log('✅ 托管订单创建成功:', escrowData)
            alert('托管订单创建成功！正在跳转到订单详情页面...')
            router.push(`/escrow/orders/${escrowData.data.escrowId}`)
          } else {
            const escrowError = await escrowResponse.json()
            console.log('❌ 托管订单创建失败:', escrowError)
            alert(`托管订单创建失败: ${escrowError.error}`)
            // 仍然跳转到普通订单页面
            router.push(`/orders/${order.id}`)
          }
        } catch (escrowError) {
          console.error('❌ 创建托管订单异常:', escrowError)
          alert('创建托管订单失败，已创建普通订单')
          router.push(`/orders/${order.id}`)
        }
      } else {
        console.log('✅ 普通订单创建完成，跳转到支付页面')
        alert('订单创建成功！正在跳转到支付页面...')
        router.push(`/orders/${order.id}/payment`)
      }

    } catch (error) {
      console.error('❌ 创建订单异常:', error)
      alert('网络错误，请稍后重试')
    } finally {
      setSubmitting(false)
    }
  }

  if (!session) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-4">请先登录</h2>
          <button
            onClick={() => router.push('/auth/signin')}
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md"
          >
            去登录
          </button>
        </div>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-lg">加载中...</div>
      </div>
    )
  }

  if (!product || !address) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-4">订单信息不完整</h2>
          <button
            onClick={() => router.back()}
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md"
          >
            返回上一页
          </button>
        </div>
      </div>
    )
  }

  const itemPrice = variant ? variant.price : product.price
  const productPrice = itemPrice * quantity
  const shippingFee = 0
  const platformFee = productPrice <= 50 ? 0.5 : productPrice <= 100 ? 1 : productPrice * 0.015
  const totalAmount = productPrice + shippingFee + platformFee + (useEscrow ? escrowFee : 0)

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-2xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow-md p-6">
          <h1 className="text-2xl font-bold mb-6">确认订单</h1>

          {/* 收货地址 */}
          <div className="mb-6">
            <h3 className="font-medium mb-3">收货地址</h3>
            <div className="border rounded-lg p-4 bg-gray-50">
              <div className="flex items-center gap-2 mb-2">
                <span className="font-medium">{address.name}</span>
                <span className="text-gray-600">{address.phone}</span>
              </div>
              <div className="text-gray-700">
                {address.province} {address.city} {address.district} {address.detail}
              </div>
            </div>
          </div>

          {/* 商品信息 */}
          <div className="mb-6">
            <h3 className="font-medium mb-3">商品信息</h3>
            <div className="border rounded-lg p-4">
              <div className="flex gap-4">
                {product.images && product.images.length > 0 && (
                  <div className="w-20 h-20 relative flex-shrink-0">
                    <Image
                      src={product.images[0]}
                      alt={product.title}
                      fill
                      className="object-cover rounded"
                    />
                  </div>
                )}
                <div className="flex-1">
                  <h4 className="font-medium mb-1">{product.title}</h4>
                  <p className="text-gray-600 text-sm mb-2">卖家：{product.seller.name}</p>
                  {variant && (
                    <p className="text-gray-600 text-sm mb-2">
                      规格：{variant.attributes?.map(attr => `${attr.name}: ${attr.value}`).join(', ')}
                    </p>
                  )}
                  <div className="flex justify-between items-center">
                    <span className="text-lg font-medium text-blue-600">{formatUSDT(itemPrice)}</span>
                    <span className="text-gray-600">数量：{quantity}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* 托管服务信息 */}
          {useEscrow && autoAssignedMediator && (
            <div className="mb-6">
              <h3 className="font-medium mb-3">托管服务</h3>
              <div className="border rounded-lg p-4 bg-blue-50">
                <div className="flex items-center gap-2 mb-2">
                  <span className="text-green-600">✓</span>
                  <span className="font-medium">已为您分配中间人</span>
                </div>
                <p className="text-gray-700 text-sm">
                  中间人：{autoAssignedMediator.mediator.name}
                </p>
                <p className="text-gray-700 text-sm">
                  托管费用：{formatUSDT(escrowFee)}
                </p>
              </div>
            </div>
          )}

          {/* 费用明细 */}
          <div className="mb-6">
            <h3 className="font-medium mb-3">费用明细</h3>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span>商品金额</span>
                <span>{formatUSDT(productPrice)}</span>
              </div>
              <div className="flex justify-between">
                <span>运费</span>
                <span>{formatUSDT(shippingFee)}</span>
              </div>
              <div className="flex justify-between">
                <span>平台服务费</span>
                <span>{formatUSDT(platformFee)}</span>
              </div>
              {useEscrow && (
                <div className="flex justify-between">
                  <span>托管服务费</span>
                  <span>{formatUSDT(escrowFee)}</span>
                </div>
              )}
              <div className="border-t pt-2 flex justify-between font-medium text-lg">
                <span>总计</span>
                <span className="text-blue-600">{formatUSDT(totalAmount)}</span>
              </div>
            </div>
          </div>

          {/* 提交按钮 */}
          <button
            onClick={handleCreateOrder}
            disabled={submitting}
            className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-300 text-white py-3 px-4 rounded-md font-medium"
          >
            {submitting ? '提交中...' : '确认下单'}
          </button>
        </div>
      </div>
    </div>
  )
}
