import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import jwt from 'jsonwebtoken'

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    // 生成WebSocket认证token
    const token = jwt.sign(
      { 
        userId: session.user.id,
        email: session.user.email,
        name: session.user.name
      },
      process.env.JWT_SECRET || 'fallback-secret',
      { expiresIn: '24h' }
    )

    return NextResponse.json({ token })

  } catch (error) {
    console.error('Generate socket token error:', error)
    return NextResponse.json(
      { error: '生成token失败' },
      { status: 500 }
    )
  }
}
