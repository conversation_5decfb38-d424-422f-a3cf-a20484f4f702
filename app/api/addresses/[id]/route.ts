import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// 获取单个地址详情
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const address = await prisma.address.findFirst({
      where: {
        id: params.id,
        userId: session.user.id
      }
    })

    if (!address) {
      return NextResponse.json(
        { error: '地址不存在' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: address
    })

  } catch (error) {
    console.error('获取地址详情失败:', error)
    return NextResponse.json(
      { error: '获取地址详情失败' },
      { status: 500 }
    )
  }
}

// 更新地址
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const {
      name,
      phone,
      province,
      city,
      district,
      detail,
      isDefault = false
    } = body

    // 验证地址是否存在且属于当前用户
    const existingAddress = await prisma.address.findFirst({
      where: {
        id: params.id,
        userId: session.user.id
      }
    })

    if (!existingAddress) {
      return NextResponse.json(
        { error: '地址不存在' },
        { status: 404 }
      )
    }

    // 验证必填字段
    if (!name || !phone || !province || !city || !district || !detail) {
      return NextResponse.json(
        { error: '地址信息不完整，请填写所有必填字段' },
        { status: 400 }
      )
    }

    // 验证手机号格式
    const phoneRegex = /^1[3-9]\d{9}$/
    if (!phoneRegex.test(phone)) {
      return NextResponse.json(
        { error: '手机号格式不正确' },
        { status: 400 }
      )
    }

    // 验证姓名长度
    if (name.length < 2 || name.length > 20) {
      return NextResponse.json(
        { error: '收件人姓名长度应在2-20个字符之间' },
        { status: 400 }
      )
    }

    // 验证详细地址长度
    if (detail.length < 5 || detail.length > 200) {
      return NextResponse.json(
        { error: '详细地址长度应在5-200个字符之间' },
        { status: 400 }
      )
    }

    // 使用事务处理默认地址设置
    const result = await prisma.$transaction(async (tx) => {
      // 如果设置为默认地址，先取消其他默认地址
      if (isDefault && !existingAddress.isDefault) {
        await tx.address.updateMany({
          where: {
            userId: session.user.id,
            isDefault: true
          },
          data: {
            isDefault: false
          }
        })
      }

      // 更新地址
      const updatedAddress = await tx.address.update({
        where: {
          id: params.id
        },
        data: {
          name,
          phone,
          province,
          city,
          district,
          detail,
          isDefault
        }
      })

      return updatedAddress
    })

    return NextResponse.json({
      success: true,
      message: '地址更新成功',
      data: result
    })

  } catch (error) {
    console.error('更新地址失败:', error)
    return NextResponse.json(
      { error: '更新地址失败' },
      { status: 500 }
    )
  }
}

// 删除单个地址
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    // 验证地址是否存在且属于当前用户
    const address = await prisma.address.findFirst({
      where: {
        id: params.id,
        userId: session.user.id
      }
    })

    if (!address) {
      return NextResponse.json(
        { error: '地址不存在' },
        { status: 404 }
      )
    }

    // 删除地址
    await prisma.address.delete({
      where: {
        id: params.id
      }
    })

    return NextResponse.json({
      success: true,
      message: '地址删除成功'
    })

  } catch (error) {
    console.error('删除地址失败:', error)
    return NextResponse.json(
      { error: '删除地址失败' },
      { status: 500 }
    )
  }
}
