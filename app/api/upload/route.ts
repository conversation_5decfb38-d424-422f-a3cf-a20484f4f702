import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth'
import { writeFile, mkdir } from 'fs/promises'
import { join } from 'path'
import sharp from 'sharp'

// 支持的图片类型
const ALLOWED_TYPES = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp']
const MAX_FILE_SIZE = 5 * 1024 * 1024 // 5MB
const MAX_AVATAR_SIZE = 2 * 1024 * 1024 // 2MB for avatars

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const formData = await request.formData()
    const file = formData.get('file') as File
    const type = formData.get('type') as string // 'product', 'avatar', 'payment'

    if (!file) {
      return NextResponse.json(
        { error: '未选择文件' },
        { status: 400 }
      )
    }

    // 验证文件类型
    if (!ALLOWED_TYPES.includes(file.type)) {
      return NextResponse.json(
        { error: '不支持的文件类型，请上传 JPG、PNG 或 WebP 格式的图片' },
        { status: 400 }
      )
    }

    // 验证文件大小
    const maxSize = type === 'avatar' ? MAX_AVATAR_SIZE : MAX_FILE_SIZE
    const maxSizeText = type === 'avatar' ? '2MB' : '5MB'

    if (file.size > maxSize) {
      return NextResponse.json(
        { error: `文件大小不能超过 ${maxSizeText}` },
        { status: 400 }
      )
    }

    // 验证上传类型
    if (!['product', 'avatar', 'payment'].includes(type)) {
      return NextResponse.json(
        { error: '无效的上传类型' },
        { status: 400 }
      )
    }

    // 创建文件名
    const timestamp = Date.now()
    const randomString = Math.random().toString(36).substring(2, 8)
    const extension = file.name.split('.').pop()
    const fileName = `${type}_${session.user.id}_${timestamp}_${randomString}.${extension}`

    // 创建上传目录
    const uploadDir = join(process.cwd(), 'public', 'uploads', type)
    try {
      await mkdir(uploadDir, { recursive: true })
    } catch (error) {
      // 目录可能已存在，忽略错误
    }

    // 读取文件内容
    const bytes = await file.arrayBuffer()
    const buffer = Buffer.from(bytes)

    // 使用 Sharp 处理图片
    let processedBuffer: Buffer = buffer

    if (type === 'product') {
      // 商品图片：压缩并调整大小
      processedBuffer = await sharp(Buffer.from(buffer))
        .resize(800, 600, { 
          fit: 'inside',
          withoutEnlargement: true 
        })
        .jpeg({ quality: 85 })
        .toBuffer()
    } else if (type === 'avatar') {
      // 头像：验证尺寸并裁剪为正方形
      const metadata = await sharp(buffer).metadata()

      // 检查图片尺寸是否在要求范围内（200x200到500x500像素）
      if (metadata.width && metadata.height) {
        const minSize = Math.min(metadata.width, metadata.height)
        if (minSize < 200) {
          return NextResponse.json(
            { error: '头像图片尺寸太小，最小尺寸为200x200像素' },
            { status: 400 }
          )
        }
        // 允许大于500像素的图片，会自动缩放
      }

      // 裁剪为200x200正方形头像（居中裁剪）
      processedBuffer = await sharp(Buffer.from(buffer))
        .resize(200, 200, {
          fit: 'cover',
          position: 'center'
        })
        .jpeg({ quality: 90 })
        .toBuffer()
    } else if (type === 'payment') {
      // 支付凭证：保持原比例，适度压缩
      processedBuffer = await sharp(Buffer.from(buffer))
        .resize(1200, 1200, { 
          fit: 'inside',
          withoutEnlargement: true 
        })
        .jpeg({ quality: 80 })
        .toBuffer()
    }

    // 保存文件
    const filePath = join(uploadDir, fileName)
    await writeFile(filePath, processedBuffer)

    // 返回文件URL
    const fileUrl = `/uploads/${type}/${fileName}`

    return NextResponse.json({
      success: true,
      url: fileUrl,
      fileName,
      size: processedBuffer.length,
      originalSize: file.size
    })

  } catch (error) {
    console.error('Upload error:', error)
    return NextResponse.json(
      { error: '文件上传失败' },
      { status: 500 }
    )
  }
}
