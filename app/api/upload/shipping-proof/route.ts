import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { writeFile, mkdir } from 'fs/promises'
import { join } from 'path'
import { existsSync } from 'fs'

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const formData = await request.formData()
    const files = formData.getAll('files') as File[]

    if (!files || files.length === 0) {
      return NextResponse.json(
        { error: '请选择要上传的文件' },
        { status: 400 }
      )
    }

    // 限制文件数量
    if (files.length > 5) {
      return NextResponse.json(
        { error: '最多只能上传5张图片' },
        { status: 400 }
      )
    }

    const uploadedFiles = []
    const uploadDir = join(process.cwd(), 'public', 'uploads', 'shipping-proof')

    // 确保上传目录存在
    if (!existsSync(uploadDir)) {
      await mkdir(uploadDir, { recursive: true })
    }

    for (const file of files) {
      // 验证文件类型
      if (!file.type.startsWith('image/')) {
        return NextResponse.json(
          { error: `文件 ${file.name} 不是有效的图片格式` },
          { status: 400 }
        )
      }

      // 验证文件大小 (5MB)
      if (file.size > 5 * 1024 * 1024) {
        return NextResponse.json(
          { error: `文件 ${file.name} 大小超过5MB限制` },
          { status: 400 }
        )
      }

      // 生成唯一文件名
      const timestamp = Date.now()
      const randomString = Math.random().toString(36).substring(2, 15)
      const fileExtension = file.name.split('.').pop()
      const fileName = `${timestamp}_${randomString}.${fileExtension}`

      // 保存文件
      const bytes = await file.arrayBuffer()
      const buffer = Buffer.from(bytes)
      const filePath = join(uploadDir, fileName)
      
      await writeFile(filePath, buffer)

      // 生成访问URL
      const fileUrl = `/uploads/shipping-proof/${fileName}`
      
      uploadedFiles.push({
        originalName: file.name,
        fileName: fileName,
        url: fileUrl,
        size: file.size,
        type: file.type
      })
    }

    return NextResponse.json({
      success: true,
      message: `成功上传 ${uploadedFiles.length} 个文件`,
      data: uploadedFiles
    })

  } catch (error) {
    console.error('上传发货凭证失败:', error)
    return NextResponse.json(
      { error: '上传失败' },
      { status: 500 }
    )
  }
}

// 获取支持的文件类型信息
export async function GET() {
  return NextResponse.json({
    success: true,
    data: {
      maxFiles: 5,
      maxFileSize: 5 * 1024 * 1024, // 5MB
      allowedTypes: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'],
      allowedExtensions: ['.jpg', '.jpeg', '.png', '.gif', '.webp']
    }
  })
}
