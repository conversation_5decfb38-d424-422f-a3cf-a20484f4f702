import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// 调整用户保证金余额
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: '请先登录' },
        { status: 401 }
      )
    }

    // 检查管理员权限
    const admin = await prisma.user.findUnique({
      where: { email: session.user.email }
    })

    if (!admin || admin.role !== 'ADMIN') {
      return NextResponse.json(
        { error: '无管理员权限' },
        { status: 403 }
      )
    }

    const { userId, amount, reason } = await request.json()

    // 验证输入
    if (!userId || !amount || !reason) {
      return NextResponse.json(
        { error: '缺少必要参数' },
        { status: 400 }
      )
    }

    if (typeof amount !== 'number' || amount === 0) {
      return NextResponse.json(
        { error: '调整金额必须是非零数字' },
        { status: 400 }
      )
    }

    if (!reason.trim()) {
      return NextResponse.json(
        { error: '请输入调整原因' },
        { status: 400 }
      )
    }

    // 获取目标用户
    const targetUser = await prisma.user.findUnique({
      where: { id: userId }
    })

    if (!targetUser) {
      return NextResponse.json(
        { error: '用户不存在' },
        { status: 404 }
      )
    }

    const currentBalance = targetUser.depositBalance || 0
    const newBalance = currentBalance + amount

    if (newBalance < 0) {
      return NextResponse.json(
        { error: '调整后余额不能为负数' },
        { status: 400 }
      )
    }

    // 使用事务进行余额调整
    const result = await prisma.$transaction(async (tx) => {
      // 更新用户余额
      const updatedUser = await tx.user.update({
        where: { id: userId },
        data: {
          depositBalance: newBalance
        }
      })

      // 记录操作历史
      const operation = await tx.depositOperation.create({
        data: {
          userId: userId,
          operatorId: admin.id,
          type: amount > 0 ? 'DEPOSIT' : 'WITHDRAWAL',
          amount: amount,
          balanceBefore: currentBalance,
          balanceAfter: newBalance,
          description: `管理员调整: ${reason.trim()}`,
          status: 'COMPLETED'
        }
      })

      return { updatedUser, operation }
    })

    return NextResponse.json({
      success: true,
      message: '余额调整成功',
      data: {
        userId: result.updatedUser.id,
        oldBalance: currentBalance,
        newBalance: newBalance,
        adjustmentAmount: amount,
        reason: reason.trim(),
        operationId: result.operation.id
      }
    })

  } catch (error) {
    console.error('余额调整失败:', error)
    return NextResponse.json(
      { error: '余额调整失败，请稍后重试' },
      { status: 500 }
    )
  }
}
