import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { checkAndUpdateProductStock } from '@/lib/inventory'
import { handleOrderStatusChange, handlePaymentConfirmed } from '@/lib/systemMessage'
import { sendOrderStatusNotification, sendPaymentConfirmedNotification } from '@/lib/notifications'

// 获取单个订单详情
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const { id } = await params
    const order = await prisma.order.findUnique({
      where: {
        id: id
      },
      include: {
        product: {
          select: {
            id: true,
            title: true,
            description: true,
            price: true,
            images: true,
            category: true,
            condition: true
          }
        },
        buyer: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        seller: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    })

    if (!order) {
      return NextResponse.json(
        { error: '订单不存在' },
        { status: 404 }
      )
    }

    // 检查权限：只有买家或卖家可以查看订单
    if (order.buyerId !== session.user.id && order.sellerId !== session.user.id) {
      return NextResponse.json(
        { error: '无权限查看此订单' },
        { status: 403 }
      )
    }

    return NextResponse.json(order)

  } catch (error) {
    console.error('Get order error:', error)
    return NextResponse.json(
      { error: '获取订单详情失败' },
      { status: 500 }
    )
  }
}

// 更新订单状态
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const {
      action,
      paymentScreenshot,
      paymentTxHash,
      trackingNumber,
      shippingCompany,
      refundReason
    } = body

    // 获取订单信息
    const order = await prisma.order.findUnique({
      where: { id: params.id },
      include: {
        product: true
      }
    })

    if (!order) {
      return NextResponse.json(
        { error: '订单不存在' },
        { status: 404 }
      )
    }

    let updateData: any = {}

    switch (action) {
      case 'upload_payment':
        // 买家上传支付凭证
        if (order.buyerId !== session.user.id) {
          return NextResponse.json(
            { error: '无权限执行此操作' },
            { status: 403 }
          )
        }
        if (order.status !== 'PENDING_PAYMENT') {
          return NextResponse.json(
            { error: '订单状态不允许此操作' },
            { status: 400 }
          )
        }
        updateData = {
          status: 'PAID',
          paymentScreenshot,
          paymentTxHash,
          paymentConfirmed: false
        }
        break

      case 'confirm_payment':
        // 管理员确认支付（这里简化为卖家确认）
        if (order.sellerId !== session.user.id) {
          return NextResponse.json(
            { error: '无权限执行此操作' },
            { status: 403 }
          )
        }
        if (order.status !== 'PAID') {
          return NextResponse.json(
            { error: '订单状态不允许此操作' },
            { status: 400 }
          )
        }
        updateData = {
          paymentConfirmed: true
        }
        // 发送支付确认系统消息
        await handlePaymentConfirmed(order.id, order.buyerId)
        break

      case 'ship':
        // 卖家发货
        if (order.sellerId !== session.user.id) {
          return NextResponse.json(
            { error: '无权限执行此操作' },
            { status: 403 }
          )
        }
        if (order.status !== 'PAID' || !order.paymentConfirmed) {
          return NextResponse.json(
            { error: '订单状态不允许此操作' },
            { status: 400 }
          )
        }
        if (!trackingNumber) {
          return NextResponse.json(
            { error: '请提供快递单号' },
            { status: 400 }
          )
        }
        updateData = {
          status: 'SHIPPED',
          trackingNumber,
          shippingCompany: shippingCompany || '未指定'
        }
        break

      case 'confirm_received':
        // 买家确认收货
        if (order.buyerId !== session.user.id) {
          return NextResponse.json(
            { error: '无权限执行此操作' },
            { status: 403 }
          )
        }
        if (order.status !== 'SHIPPED') {
          return NextResponse.json(
            { error: '订单状态不允许此操作' },
            { status: 400 }
          )
        }
        updateData = {
          status: 'COMPLETED',
          receivedAt: new Date()
        }
        break

      case 'request_refund':
        // 买家申请退款
        if (order.buyerId !== session.user.id) {
          return NextResponse.json(
            { error: '无权限执行此操作' },
            { status: 403 }
          )
        }
        if (!['PAID', 'SHIPPED'].includes(order.status)) {
          return NextResponse.json(
            { error: '订单状态不允许此操作' },
            { status: 400 }
          )
        }
        updateData = {
          status: 'REFUND_REQUESTED',
          refundReason: refundReason || '买家申请退款'
        }
        break

      case 'cancel':
        // 取消订单
        if (order.buyerId !== session.user.id && order.sellerId !== session.user.id) {
          return NextResponse.json(
            { error: '无权限执行此操作' },
            { status: 403 }
          )
        }
        if (order.status !== 'PENDING_PAYMENT') {
          return NextResponse.json(
            { error: '订单状态不允许此操作' },
            { status: 400 }
          )
        }
        updateData = {
          status: 'CANCELLED'
        }

        // 恢复商品库存并检查是否需要重新上架
        await prisma.$transaction(async (tx) => {
          // 恢复库存
          await tx.product.update({
            where: { id: order.productId },
            data: {
              stock: {
                increment: 1 // 假设每个订单数量为1
              }
            }
          })

          // 检查商品是否从SOLD_OUT状态恢复为AVAILABLE
          const product = await tx.product.findUnique({
            where: { id: order.productId },
            select: {
              id: true,
              title: true,
              stock: true,
              status: true
            }
          })

          // 如果商品之前是SOLD_OUT状态且现在有库存，自动重新上架
          if (product && product.status === 'SOLD_OUT' && product.stock > 0) {
            await tx.product.update({
              where: { id: order.productId },
              data: {
                status: 'AVAILABLE'
              }
            })
            console.log(`商品 ${product.title} (ID: ${order.productId}) 库存恢复，已自动重新上架`)
          }
        })
        break

      default:
        return NextResponse.json(
          { error: '无效的操作' },
          { status: 400 }
        )
    }

    // 更新订单
    const updatedOrder = await prisma.order.update({
      where: { id: params.id },
      data: updateData,
      include: {
        product: {
          select: {
            id: true,
            title: true,
            description: true,
            price: true,
            images: true,
            category: true,
            condition: true
          }
        },
        buyer: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        seller: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    })

    // 发送订单状态变化的系统消息（除了支付确认，因为已经单独处理）
    if (action !== 'confirm_payment') {
      await handleOrderStatusChange(
        updatedOrder.id,
        updatedOrder.status,
        updatedOrder.buyerId,
        updatedOrder.sellerId
      )
    }

    // 发送实时通知
    if (action === 'confirm_payment') {
      await sendPaymentConfirmedNotification(updatedOrder.id, updatedOrder.buyerId)
    } else {
      await sendOrderStatusNotification(
        updatedOrder.id,
        updatedOrder.status,
        updatedOrder.buyerId,
        updatedOrder.sellerId
      )
    }

    return NextResponse.json(updatedOrder)

  } catch (error) {
    console.error('Update order error:', error)
    return NextResponse.json(
      { error: '更新订单失败' },
      { status: 500 }
    )
  }
}
