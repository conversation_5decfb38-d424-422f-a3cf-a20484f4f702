'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { formatUSDT } from '@/lib/utils'
import Navbar from '@/components/Navbar'
import { ProductStockStatus } from '@/components/inventory/StockAlert'
import VariantSelector from '@/components/variants/VariantSelector'
import FavoriteButton from '@/components/FavoriteButton'


interface VariantAttribute {
  id: string
  name: string
  value: string
}

interface ProductVariant {
  id: string
  sku?: string
  price: number
  stock: number
  status: string
  isDefault: boolean
  attributes: VariantAttribute[]
}

interface Mediator {
  id: string
  name: string
  avatar?: string
  mediatorFeeRate: number
  mediatorDeposit: number
  mediatorReputation: number
  mediatorVerifiedAt: string
  availableDeposit?: number
  estimatedFee?: number
}

interface Product {
  id: string
  title: string
  description: string
  images: string
  price: number
  category: string
  condition: string
  city: string | null
  district: string | null
  shippingFrom: string | null
  stock: number
  status: string
  hasVariants: boolean
  createdAt: string
  seller: {
    id: string
    name: string | null
    creditScore: number
    city: string | null
    district: string | null
  }
  variants?: ProductVariant[]
}

export default function ProductDetailPage({ params }: { params: Promise<{ id: string }> }) {
  const { data: session } = useSession()
  const router = useRouter()
  const [product, setProduct] = useState<Product | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isUpdatingStatus, setIsUpdatingStatus] = useState(false)
  const [isDeleting, setIsDeleting] = useState(false)
  const [quantity, setQuantity] = useState(1)
  const [productId, setProductId] = useState<string>('')
  const [selectedVariant, setSelectedVariant] = useState<ProductVariant | null>(null)
  const [variants, setVariants] = useState<ProductVariant[]>([])
  const [loadingVariants, setLoadingVariants] = useState(false)
  const [canPurchase, setCanPurchase] = useState(false) // 默认为false，需要验证后才能购买
  const [purchaseError, setPurchaseError] = useState('')
  const [isCheckingPermission, setIsCheckingPermission] = useState(true)

  // 托管相关状态
  const [useEscrow, setUseEscrow] = useState(false)
  const [autoAssignedMediator, setAutoAssignedMediator] = useState<any>(null)
  const [assigningMediator, setAssigningMediator] = useState(false)
  const [escrowFee, setEscrowFee] = useState(0)

  useEffect(() => {
    const initParams = async () => {
      const { id } = await params
      setProductId(id)
    }
    initParams()
  }, [params])

  useEffect(() => {
    if (productId) {
      fetchProduct()
    }
  }, [productId])

  // 检查用户购买权限
  useEffect(() => {
    const checkPurchasePermission = async () => {
      if (!session?.user?.id) {
        setCanPurchase(false)
        setPurchaseError('')
        setIsCheckingPermission(false)
        return
      }

      setIsCheckingPermission(true)
      try {
        const response = await fetch('/api/user/status', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
        })

        if (response.ok) {
          const data = await response.json()
          if (data.status.isBanned) {
            setCanPurchase(false)
            setPurchaseError('您的账户已被封禁，无法购买商品')
          } else if (data.status.isSuspended) {
            setCanPurchase(false)
            setPurchaseError('您的账户已被暂停，无法购买商品')
          } else if (data.status.isActive) {
            setCanPurchase(true)
            setPurchaseError('')
          } else {
            setCanPurchase(false)
            setPurchaseError('账户状态异常，无法购买商品')
          }
        } else {
          console.error('检查用户状态失败:', response.status)
          setCanPurchase(false)
          setPurchaseError('无法验证账户状态，请稍后重试')
        }
      } catch (error) {
        console.error('检查购买权限失败:', error)
        setCanPurchase(false)
        setPurchaseError('网络错误，请检查网络连接')
      } finally {
        setIsCheckingPermission(false)
      }
    }

    checkPurchasePermission()
  }, [session])

  // 自动分配中间人
  const autoAssignMediator = async (orderAmount: number) => {
    if (orderAmount < 100) {
      setAutoAssignedMediator(null)
      setEscrowFee(0)
      return
    }

    setAssigningMediator(true)
    try {
      const response = await fetch('/api/mediator/auto-assign', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          orderAmount
        })
      })

      const data = await response.json()

      if (data.success) {
        setAutoAssignedMediator(data.data)
        setEscrowFee(data.data.escrowFee)
      } else {
        console.error('自动分配中间人失败:', data.error)
        setAutoAssignedMediator(null)
        setEscrowFee(0)
        alert(data.error || '自动分配中间人失败，请稍后重试')
      }
    } catch (error) {
      console.error('自动分配中间人失败:', error)
      setAutoAssignedMediator(null)
      setEscrowFee(0)
      alert('网络错误，请稍后重试')
    } finally {
      setAssigningMediator(false)
    }
  }

  // 当商品价格变化时，重新自动分配中间人
  useEffect(() => {
    if (product && useEscrow) {
      const orderAmount = selectedVariant ? selectedVariant.price * quantity : product.price * quantity
      autoAssignMediator(orderAmount)
    } else {
      setAutoAssignedMediator(null)
      setEscrowFee(0)
    }
  }, [product, selectedVariant, quantity, useEscrow])

  const fetchProduct = async () => {
    try {
      const response = await fetch(`/api/products/${productId}`)
      if (response.ok) {
        const data = await response.json()
        setProduct(data)

        // 如果商品有变体，获取变体信息
        if (data.hasVariants) {
          fetchVariants()
        }
      } else if (response.status === 404) {
        router.push('/products')
      }
    } catch (error) {
      console.error('Failed to fetch product:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const fetchVariants = async () => {
    try {
      setLoadingVariants(true)
      const response = await fetch(`/api/products/${productId}/variants`)
      if (response.ok) {
        const variantData = await response.json()
        setVariants(variantData)

        // 设置默认选中的变体
        const defaultVariant = variantData.find((v: ProductVariant) => v.isDefault) || variantData[0]
        if (defaultVariant) {
          setSelectedVariant(defaultVariant)
        }
      }
    } catch (error) {
      console.error('Failed to fetch variants:', error)
    } finally {
      setLoadingVariants(false)
    }
  }

  // 解析图片字符串
  const parseImages = (images: string | null): string[] => {
    if (!images) return []

    try {
      // 尝试解析为JSON数组
      const parsed = JSON.parse(images)
      if (Array.isArray(parsed)) {
        return parsed.filter(img => img && typeof img === 'string')
      }
    } catch (e) {
      // 如果不是JSON，按逗号分割
      return images.split(',').map(s => s.trim()).filter(s => s)
    }

    return []
  }

  const getConditionText = (condition: string) => {
    const conditionMap: Record<string, string> = {
      'NEW': '全新',
      'USED_LIKE_NEW': '九成新',
      'USED_GOOD': '有使用痕迹但不影响使用',
      'USED_FAIR': '影响使用但有收藏价值'
    }
    return conditionMap[condition] || condition
  }

  const getCategoryText = (category: string) => {
    const categoryMap: Record<string, string> = {
      'GENERAL': '一般商品',
      'ELECTRONICS': '电子产品',
      'CLOTHING': '服装配饰',
      'BOOKS': '图书文具',
      'HOME': '家居用品',
      'SPORTS': '运动户外',
      'VIRTUAL': '虚拟物品'
    }
    return categoryMap[category] || category
  }

  const updateProductStatus = async (newStatus: string) => {
    if (!product) return

    setIsUpdatingStatus(true)
    try {
      const response = await fetch(`/api/products/${product.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          status: newStatus
        }),
      })

      if (response.ok) {
        const updatedProduct = await response.json()
        setProduct(updatedProduct)
        alert(newStatus === 'INACTIVE' ? '商品已下架' : '商品已重新上架')
      } else {
        const errorData = await response.json()
        alert(errorData.error || '操作失败，请重试')
      }
    } catch (error) {
      alert('网络错误，请重试')
    } finally {
      setIsUpdatingStatus(false)
    }
  }

  const handleToggleStatus = () => {
    if (!product) return

    const newStatus = product.status === 'AVAILABLE' ? 'INACTIVE' : 'AVAILABLE'
    const action = newStatus === 'INACTIVE' ? '下架' : '重新上架'

    if (confirm(`确定要${action}这个商品吗？`)) {
      updateProductStatus(newStatus)
    }
  }

  const deleteProduct = async () => {
    if (!product) return

    setIsDeleting(true)
    try {
      const response = await fetch(`/api/products/${product.id}`, {
        method: 'DELETE',
      })

      if (response.ok) {
        alert('商品删除成功')
        router.push('/products')
      } else {
        const errorData = await response.json()
        alert(errorData.error || '删除失败，请重试')
      }
    } catch (error) {
      alert('网络错误，请重试')
    } finally {
      setIsDeleting(false)
    }
  }

  const handleDeleteProduct = () => {
    if (!product) return

    if (confirm('确定要删除这个商品吗？删除后无法恢复！')) {
      deleteProduct()
    }
  }

  const handlePurchase = async () => {
    console.log('=== 开始购买流程 ===')
    console.log('Session:', session)
    console.log('Use Escrow:', useEscrow)

    // 双重检查：确保用户已登录
    if (!session?.user?.id) {
      console.log('❌ 用户未登录，跳转到登录页面')
      alert('请先登录后再购买商品')
      router.push('/auth/signin')
      return
    }

    console.log('✅ 用户已登录:', session.user.email)

    // 检查是否是商品所有者
    if (session.user.id === product?.seller.id) {
      alert('不能购买自己的商品')
      return
    }

    // 检查购买权限
    if (!canPurchase) {
      alert(purchaseError || '您当前无法购买商品')
      return
    }

    if (!product) {
      alert('商品信息加载中，请稍后重试')
      return
    }

    // 再次验证商品状态
    const itemToCheck = selectedVariant || product
    if (itemToCheck.status !== 'AVAILABLE' || itemToCheck.stock < quantity) {
      alert('商品库存不足或已下架')
      return
    }

    // 跳转到地址选择页面
    const params = new URLSearchParams({
      productId: product.id,
      quantity: quantity.toString(),
      useEscrow: useEscrow.toString()
    })

    if (selectedVariant) {
      params.append('variantId', selectedVariant.id)
    }

    console.log('✅ 跳转到地址选择页面')
    router.push(`/checkout/address?${params.toString()}`)
  }




  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-lg">加载中...</div>
      </div>
    )
  }

  if (!product) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">商品不存在</h2>
          <Link
            href="/products"
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md text-sm font-medium"
          >
            返回商品列表
          </Link>
        </div>
      </div>
    )
  }

  // 严格的权限检查
  const isOwner = session?.user?.id === product.seller.id
  const isLoggedIn = !!session?.user?.id
  const canShowPurchaseButton = isLoggedIn && !isOwner && canPurchase && !isCheckingPermission

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 统一导航栏 */}
      <Navbar />

      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="px-4 py-5 sm:p-6">
              <div className="lg:grid lg:grid-cols-2 lg:gap-8">
                {/* 商品图片区域 */}
                <div className="mb-6 lg:mb-0">
                  {(() => {
                    const images = parseImages(product.images)

                    if (images.length === 0) {
                      return (
                        <div className="aspect-w-1 aspect-h-1 bg-gray-200 rounded-lg flex items-center justify-center">
                          <div className="text-gray-500 text-center">
                            <div className="text-4xl mb-2">📦</div>
                            <div>暂无图片</div>
                          </div>
                        </div>
                      )
                    }

                    if (images.length === 1) {
                      return (
                        <div className="aspect-w-1 aspect-h-1 bg-gray-200 rounded-lg overflow-hidden">
                          <img
                            src={images[0]}
                            alt={product.title}
                            className="w-full h-full object-cover"
                            onError={(e) => {
                              const target = e.target as HTMLImageElement
                              target.style.display = 'none'
                              const parent = target.parentElement
                              if (parent) {
                                parent.innerHTML = `
                                  <div class="flex items-center justify-center h-full text-gray-500 text-center">
                                    <div>
                                      <div class="text-4xl mb-2">🖼️</div>
                                      <div>图片加载失败</div>
                                    </div>
                                  </div>
                                `
                              }
                            }}
                          />
                        </div>
                      )
                    }

                    // 多张图片的情况
                    return (
                      <div className="space-y-4">
                        <div className="aspect-w-1 aspect-h-1 bg-gray-200 rounded-lg overflow-hidden">
                          <img
                            src={images[0]}
                            alt={product.title}
                            className="w-full h-full object-cover"
                            onError={(e) => {
                              const target = e.target as HTMLImageElement
                              target.style.display = 'none'
                              const parent = target.parentElement
                              if (parent) {
                                parent.innerHTML = `
                                  <div class="flex items-center justify-center h-full text-gray-500 text-center">
                                    <div>
                                      <div class="text-4xl mb-2">🖼️</div>
                                      <div>图片加载失败</div>
                                    </div>
                                  </div>
                                `
                              }
                            }}
                          />
                        </div>
                        {images.length > 1 && (
                          <div className="grid grid-cols-4 gap-2">
                            {images.slice(1, 5).map((image, index) => (
                              <div key={index} className="aspect-w-1 aspect-h-1 bg-gray-200 rounded overflow-hidden">
                                <img
                                  src={image}
                                  alt={`${product.title} ${index + 2}`}
                                  className="w-full h-full object-cover cursor-pointer hover:opacity-75 transition-opacity"
                                  onClick={() => {
                                    // 点击小图切换主图
                                    const mainImg = document.querySelector('.aspect-w-1.aspect-h-1 img') as HTMLImageElement
                                    if (mainImg) {
                                      mainImg.src = image
                                    }
                                  }}
                                  onError={(e) => {
                                    const target = e.target as HTMLImageElement
                                    target.style.display = 'none'
                                  }}
                                />
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    )
                  })()}
                </div>

                {/* 商品信息区域 */}
                <div>
                  <div className="flex items-center justify-between mb-4">
                    <span className="inline-flex px-3 py-1 text-sm font-semibold rounded-full bg-blue-100 text-blue-800">
                      {getCategoryText(product.category)}
                    </span>
                    <span className="inline-flex px-3 py-1 text-sm font-semibold rounded-full bg-green-100 text-green-800">
                      {getConditionText(product.condition)}
                    </span>
                  </div>

                  <div className="flex items-start justify-between mb-4">
                    <h1 className="text-3xl font-bold text-gray-900 flex-1">
                      {product.title}
                    </h1>
                    <div className="ml-4">
                      <FavoriteButton productId={product.id} size="lg" showText />
                    </div>
                  </div>

                  <div className="text-4xl font-bold text-blue-600 mb-6">
                    {selectedVariant ? formatUSDT(selectedVariant.price) : formatUSDT(product.price)}
                  </div>

                  {/* 库存状态提醒 - 只有商品所有者可见 */}
                  <ProductStockStatus
                    product={{
                      id: product.id,
                      title: product.title,
                      stock: selectedVariant ? selectedVariant.stock : product.stock,
                      status: selectedVariant ? selectedVariant.status : product.status
                    }}
                    isOwner={isOwner}
                  />

                  {/* 变体选择器 */}
                  {product.hasVariants && variants.length > 0 && (
                    <div className="mb-6">
                      <h3 className="text-lg font-medium text-gray-900 mb-4">选择规格</h3>
                      {loadingVariants ? (
                        <div className="text-center py-4">
                          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto"></div>
                          <p className="mt-2 text-sm text-gray-600">加载规格中...</p>
                        </div>
                      ) : (
                        <VariantSelector
                          variants={variants}
                          onVariantChange={setSelectedVariant}
                          disabled={false}
                        />
                      )}
                    </div>
                  )}

                  <div className="space-y-4 mb-6">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-700">库存数量:</span>
                      <span className="text-sm text-gray-900">
                        {selectedVariant ? selectedVariant.stock : product.stock} 件
                      </span>
                    </div>

                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-700">商品状态:</span>
                      <span className={`text-sm ${
                        (selectedVariant ? selectedVariant.status : product.status) === 'AVAILABLE'
                          ? 'text-green-600'
                          : 'text-red-600'
                      }`}>
                        {(selectedVariant ? selectedVariant.status : product.status) === 'AVAILABLE'
                          ? '有库存'
                          : '已售完'
                        }
                      </span>
                    </div>

                    {product.city && (
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium text-gray-700">所在地区:</span>
                        <span className="text-sm text-gray-900">
                          {product.city} {product.district}
                        </span>
                      </div>
                    )}

                    {product.shippingFrom && (
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium text-gray-700">发货地址:</span>
                        <span className="text-sm text-gray-900">{product.shippingFrom}</span>
                      </div>
                    )}
                  </div>

                  {/* 购买区域 */}
                  {!isOwner && (
                    <div className="border-t pt-6">
                      {/* 未登录用户提示 */}
                      {!isLoggedIn && (
                        <div className="text-center space-y-3">
                          <div className="text-gray-600 mb-4">请登录后购买商品</div>
                          <button
                            onClick={() => router.push('/auth/signin')}
                            className="w-full bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-md text-lg font-medium"
                          >
                            登录购买
                          </button>
                        </div>
                      )}

                      {/* 权限检查中 */}
                      {isLoggedIn && isCheckingPermission && (
                        <div className="text-center space-y-3">
                          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                          <div className="text-gray-600">检查购买权限中...</div>
                        </div>
                      )}

                      {/* 已登录但无购买权限 */}
                      {isLoggedIn && !isCheckingPermission && !canPurchase && (
                        <div className="text-center space-y-3">
                          <button
                            disabled
                            className="w-full bg-gray-400 text-white px-6 py-3 rounded-md text-lg font-medium cursor-not-allowed"
                          >
                            无法购买
                          </button>
                          <div className="text-center text-sm text-red-600">
                            {purchaseError}
                          </div>
                        </div>
                      )}

                      {/* 可以购买的情况 */}
                      {canShowPurchaseButton &&
                       (selectedVariant ? selectedVariant.status === 'AVAILABLE' && selectedVariant.stock > 0 : product.status === 'AVAILABLE' && product.stock > 0) &&
                       (!product.hasVariants || selectedVariant) && (
                        <>
                          <div className="flex items-center space-x-4 mb-4">
                            <label className="text-sm font-medium text-gray-700">购买数量:</label>
                            <select
                              value={quantity}
                              onChange={(e) => setQuantity(parseInt(e.target.value))}
                              className="border border-gray-300 rounded-md px-3 py-1 text-sm focus:ring-blue-500 focus:border-blue-500"
                            >
                              {Array.from({
                                length: Math.min(selectedVariant ? selectedVariant.stock : product.stock, 10)
                              }, (_, i) => i + 1).map(num => (
                                <option key={num} value={num}>{num}</option>
                              ))}
                            </select>
                          </div>

                          <div className="flex items-center justify-between mb-4">
                            <span className="text-lg font-medium text-gray-900">
                              总价: {formatUSDT((selectedVariant ? selectedVariant.price : product.price) * quantity)}
                            </span>
                          </div>

                          {/* 托管服务选择 */}
                          {((selectedVariant ? selectedVariant.price : product.price) * quantity) >= 100 && (
                            <div className="border border-gray-200 rounded-lg p-4 mb-4 bg-blue-50">
                              <div className="flex items-center space-x-2 mb-3">
                                <input
                                  type="checkbox"
                                  id="useEscrow"
                                  checked={useEscrow}
                                  onChange={(e) => {
                                    setUseEscrow(e.target.checked)
                                    if (!e.target.checked) {
                                      setAutoAssignedMediator(null)
                                      setEscrowFee(0)
                                    }
                                  }}
                                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                />
                                <label htmlFor="useEscrow" className="text-sm font-medium text-gray-900">
                                  使用中间人托管服务 🛡️
                                </label>
                              </div>

                              <p className="text-xs text-gray-600 mb-3">
                                系统将自动为您分配最优中间人，提供资金担保确保交易安全
                              </p>

                              {useEscrow && (
                                <div className="space-y-3">
                                  {assigningMediator ? (
                                    <div className="flex items-center space-x-2">
                                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                                      <span className="text-sm text-gray-600">正在为您智能分配最优中间人...</span>
                                    </div>
                                  ) : autoAssignedMediator ? (
                                    <div className="bg-white rounded-lg p-4 border border-green-200 bg-green-50">
                                      <div className="flex items-start space-x-3">
                                        <div className="flex-shrink-0">
                                          <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                                            <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                            </svg>
                                          </div>
                                        </div>
                                        <div className="flex-1">
                                          <h4 className="text-sm font-medium text-green-900 mb-1">
                                            已为您分配中间人: {autoAssignedMediator.mediator.name}
                                          </h4>
                                          <p className="text-xs text-green-700 mb-2">
                                            {autoAssignedMediator.assignmentReason}
                                          </p>
                                          <div className="grid grid-cols-2 gap-2 text-xs text-green-700">
                                            <div>信誉度: {(autoAssignedMediator.mediator.reputation || 0).toFixed(1)}分</div>
                                            <div>成功率: {(autoAssignedMediator.mediator.successRate || 0).toFixed(1)}%</div>
                                            <div>费率: {((autoAssignedMediator.mediator.feeRate || 0) * 100).toFixed(1)}%</div>
                                            <div>活跃订单: {autoAssignedMediator.mediator.activeOrderCount}个</div>
                                          </div>
                                        </div>
                                      </div>

                                      <div className="mt-3 pt-3 border-t border-green-200">
                                        <div className="text-sm space-y-1">
                                          <div className="flex justify-between">
                                            <span>商品金额:</span>
                                            <span>{formatUSDT((selectedVariant ? selectedVariant.price : product.price) * quantity)}</span>
                                          </div>
                                          <div className="flex justify-between">
                                            <span>托管费用:</span>
                                            <span>{formatUSDT(escrowFee)}</span>
                                          </div>
                                          <div className="flex justify-between font-medium border-t pt-1">
                                            <span>总计:</span>
                                            <span>{formatUSDT(((selectedVariant ? selectedVariant.price : product.price) * quantity) + escrowFee)}</span>
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                  ) : (
                                    <div className="text-sm text-red-600 bg-red-50 p-3 rounded-md">
                                      暂无可用的中间人，请稍后再试或联系客服
                                    </div>
                                  )}
                                </div>
                              )}
                            </div>
                          )}

                          <div className="space-y-3">
                            <button
                              onClick={handlePurchase}
                              className="w-full bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-md text-lg font-medium"
                            >
                              立即购买
                            </button>
                          </div>
                        </>
                      )}

                      {/* 商品无库存或已下架 */}
                      {canShowPurchaseButton &&
                       (selectedVariant ? selectedVariant.status !== 'AVAILABLE' || selectedVariant.stock <= 0 : product.status !== 'AVAILABLE' || product.stock <= 0) && (
                        <div className="text-center space-y-3">
                          <button
                            disabled
                            className="w-full bg-gray-400 text-white px-6 py-3 rounded-md text-lg font-medium cursor-not-allowed"
                          >
                            {(selectedVariant ? selectedVariant.status : product.status) === 'AVAILABLE' ? '库存不足' : '商品已下架'}
                          </button>
                        </div>
                      )}

                      {/* 需要选择变体 */}
                      {canShowPurchaseButton && product.hasVariants && !selectedVariant && (
                        <div className="text-center space-y-3">
                          <button
                            disabled
                            className="w-full bg-gray-400 text-white px-6 py-3 rounded-md text-lg font-medium cursor-not-allowed"
                          >
                            请选择商品规格
                          </button>
                        </div>
                      )}
                    </div>
                  )}

                  {/* 商品所有者管理区域 */}
                  {isLoggedIn && isOwner && (
                    <div className="border-t pt-6">
                      <div className="text-sm text-gray-600 mb-4">这是您发布的商品</div>
                      <div className="flex flex-wrap gap-3">
                        <Link
                          href={`/products/${product.id}/edit`}
                          className="bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors"
                        >
                          编辑商品
                        </Link>
                        <button
                          onClick={handleToggleStatus}
                          disabled={isUpdatingStatus}
                          className={`px-4 py-2 rounded-md text-sm font-medium text-white transition-colors ${
                            product.status === 'AVAILABLE'
                              ? 'bg-red-600 hover:bg-red-700'
                              : 'bg-green-600 hover:bg-green-700'
                          } disabled:opacity-50 disabled:cursor-not-allowed`}
                        >
                          {isUpdatingStatus
                            ? '处理中...'
                            : product.status === 'AVAILABLE'
                              ? '下架商品'
                              : '重新上架'
                          }
                        </button>
                        <button
                          onClick={handleDeleteProduct}
                          disabled={isDeleting}
                          className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                        >
                          {isDeleting ? '删除中...' : '删除商品'}
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* 商品描述 */}
              <div className="mt-8 border-t pt-8">
                <h3 className="text-lg font-medium text-gray-900 mb-4">商品描述</h3>
                <div className="prose max-w-none text-gray-700 whitespace-pre-wrap">
                  {product.description}
                </div>
              </div>

              {/* 卖家信息 */}
              <div className="mt-8 border-t pt-8">
                <h3 className="text-lg font-medium text-gray-900 mb-4">卖家信息</h3>
                <div className="bg-gray-50 rounded-lg p-4">
                  <div>
                    <div className="font-medium text-gray-900">
                      {product.seller.name || '匿名用户'}
                    </div>
                    <div className="text-sm text-gray-600">
                      信誉积分: {product.seller.creditScore} 分
                    </div>
                    {product.seller.city && (
                      <div className="text-sm text-gray-600">
                        所在地: {product.seller.city} {product.seller.district}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
