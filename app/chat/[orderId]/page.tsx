'use client'

import { useState, useEffect, useRef, useCallback } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import FileUpload from '@/components/chat/FileUpload'
import MessageRenderer from '@/components/chat/MessageRenderer'
import { PaperClipIcon, ArrowPathIcon } from '@heroicons/react/24/outline'

interface Message {
  id: string
  content: string
  messageType: string
  status: string
  createdAt: string
  fileUrl?: string
  fileName?: string
  fileSize?: number
  fileMimeType?: string
  fileMetadata?: any
  sender: {
    id: string
    name: string | null
    email: string | null
  }
  receiver: {
    id: string
    name: string | null
    email: string | null
  }
}

interface Order {
  id: string
  orderNumber: string
  status: string
  product: {
    id: string
    title: string
    price: number
  }
  buyer: {
    id: string
    name: string | null
    email: string | null
  }
  seller: {
    id: string
    name: string | null
    email: string | null
  }
}

export default function ChatPage({ params }: { params: Promise<{ orderId: string }> }) {
  const { data: session } = useSession()
  const router = useRouter()
  const [order, setOrder] = useState<Order | null>(null)
  const [messages, setMessages] = useState<Message[]>([])
  const [newMessage, setNewMessage] = useState('')
  const [isLoading, setIsLoading] = useState(true)
  const [isSending, setIsSending] = useState(false)
  const [showFileUpload, setShowFileUpload] = useState(false)
  const [orderId, setOrderId] = useState<string>('')
  const [isRefreshing, setIsRefreshing] = useState(false)
  const [lastMessageCount, setLastMessageCount] = useState(0)
  const [autoRefresh, setAutoRefresh] = useState(true)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const pollingIntervalRef = useRef<NodeJS.Timeout | null>(null)

  useEffect(() => {
    const getParams = async () => {
      const resolvedParams = await params
      setOrderId(resolvedParams.orderId)
    }
    getParams()
  }, [params])

  useEffect(() => {
    if (!session) {
      router.push('/auth/signin')
      return
    }
    if (orderId) {
      fetchOrderAndMessages()
    }
  }, [session, orderId])

  // 轮询消息更新
  useEffect(() => {
    if (!orderId || !autoRefresh) return

    const startPolling = () => {
      pollingIntervalRef.current = setInterval(() => {
        fetchMessages()
      }, 3000) // 每3秒轮询一次
    }

    // 初始延迟2秒后开始轮询，避免与初始加载冲突
    const initialDelay = setTimeout(() => {
      startPolling()
    }, 2000)

    return () => {
      if (pollingIntervalRef.current) {
        clearInterval(pollingIntervalRef.current)
      }
      clearTimeout(initialDelay)
    }
  }, [orderId, autoRefresh])

  // 页面可见性变化时控制轮询
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.hidden) {
        // 页面隐藏时停止轮询
        if (pollingIntervalRef.current) {
          clearInterval(pollingIntervalRef.current)
          pollingIntervalRef.current = null
        }
      } else {
        // 页面显示时恢复轮询
        if (autoRefresh && orderId && !pollingIntervalRef.current) {
          pollingIntervalRef.current = setInterval(() => {
            fetchMessages()
          }, 3000)
        }
      }
    }

    document.addEventListener('visibilitychange', handleVisibilityChange)
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange)
    }
  }, [orderId, autoRefresh])

  useEffect(() => {
    // 自动滚动到最新消息
    scrollToBottom()
  }, [messages])

  // 组件卸载时清理轮询
  useEffect(() => {
    return () => {
      if (pollingIntervalRef.current) {
        clearInterval(pollingIntervalRef.current)
      }
    }
  }, [])

  const fetchOrderAndMessages = async () => {
    setIsLoading(true)
    try {
      // 获取订单信息
      const orderResponse = await fetch(`/api/orders/${orderId}`)
      if (orderResponse.ok) {
        const orderData = await orderResponse.json()
        setOrder(orderData)
      } else if (orderResponse.status === 404) {
        router.push('/orders')
        return
      }

      // 获取消息
      await fetchMessages()
    } catch (error) {
      console.error('Failed to fetch data:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const fetchMessages = async () => {
    try {
      const messagesResponse = await fetch(`/api/messages?orderId=${orderId}`)
      if (messagesResponse.ok) {
        const messagesData = await messagesResponse.json()
        const newMessages = messagesData.messages

        // 检查是否有新消息
        if (newMessages.length !== lastMessageCount) {
          setMessages(newMessages)
          setLastMessageCount(newMessages.length)

          // 如果有新消息，滚动到底部
          if (newMessages.length > lastMessageCount) {
            setTimeout(() => scrollToBottom(), 100)
          }
        }
      }
    } catch (error) {
      console.error('Failed to fetch messages:', error)
    }
  }

  const manualRefresh = async () => {
    setIsRefreshing(true)
    await fetchMessages()
    setTimeout(() => setIsRefreshing(false), 500)
  }

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  // 处理输入变化
  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    setNewMessage(value)
  }, [])

  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!newMessage.trim() || isSending) {
      return
    }

    setIsSending(true)
    const messageContent = newMessage.trim()
    setNewMessage('') // 立即清空输入框

    try {
      const response = await fetch('/api/messages', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          orderId: orderId,
          content: messageContent,
          messageType: 'TEXT'
        }),
      })

      if (response.ok) {
        const message = await response.json()
        setMessages(prev => [...prev, message])
        setLastMessageCount(prev => prev + 1)
        // 发送消息后立即滚动到底部
        setTimeout(() => scrollToBottom(), 100)
      } else {
        const data = await response.json()
        alert(data.error || '发送消息失败')
        setNewMessage(messageContent) // 恢复消息内容
      }
    } catch (error) {
      console.error('Failed to send message:', error)
      alert('发送消息失败，请稍后重试')
      setNewMessage(messageContent) // 恢复消息内容
    } finally {
      setIsSending(false)
    }
  }

  const handleFileUploaded = async (fileData: any) => {
    setIsSending(true)
    try {
      const response = await fetch('/api/messages', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          orderId: orderId,
          content: '',
          messageType: fileData.messageType,
          fileUrl: fileData.fileUrl,
          fileName: fileData.fileName,
          fileSize: fileData.fileSize,
          fileMimeType: fileData.fileMimeType,
          fileMetadata: fileData.metadata
        }),
      })

      if (response.ok) {
        const message = await response.json()
        setMessages(prev => [...prev, message])
        setLastMessageCount(prev => prev + 1)
        setShowFileUpload(false) // 隐藏文件上传区域
        // 发送文件后立即滚动到底部
        setTimeout(() => scrollToBottom(), 100)
      } else {
        const data = await response.json()
        alert(data.error || '发送文件消息失败')
      }
    } catch (error) {
      alert('网络错误，请稍后重试')
    } finally {
      setIsSending(false)
    }
  }

  const formatTime = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60)

    if (diffInHours < 24) {
      return date.toLocaleTimeString('zh-CN', { 
        hour: '2-digit', 
        minute: '2-digit' 
      })
    } else {
      return date.toLocaleDateString('zh-CN', { 
        month: 'short', 
        day: 'numeric',
        hour: '2-digit', 
        minute: '2-digit' 
      })
    }
  }

  const getOtherParty = () => {
    if (!order || !session?.user?.id) return null
    return order.buyer.id === session.user.id ? order.seller : order.buyer
  }

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-lg">加载中...</div>
      </div>
    )
  }

  if (!order) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">订单不存在</h2>
          <Link
            href="/orders"
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md text-sm font-medium"
          >
            返回订单列表
          </Link>
        </div>
      </div>
    )
  }

  const otherParty = getOtherParty()

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      {/* 导航栏 */}
      <nav className="bg-white shadow-sm border-b">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <Link href="/" className="flex items-center hover:opacity-80 transition-opacity">
                <img
                  src="/logo.jpg"
                  alt="BitMarket Logo"
                  className="w-10 h-10 rounded-lg object-cover"
                />
              </Link>
            </div>
            <div className="flex items-center space-x-4">
              <Link href="/orders" className="text-gray-700 hover:text-gray-900">
                我的订单
              </Link>
            </div>
          </div>
        </div>
      </nav>

      {/* 聊天头部 */}
      <div className="bg-white border-b px-4 py-3">
        <div className="max-w-4xl mx-auto">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-lg font-medium text-gray-900">
                与 {otherParty?.name || '匿名用户'} 的对话
              </h1>
              <p className="text-sm text-gray-600">
                订单: {order.product.title} | {order.orderNumber}
              </p>
              <div className="flex items-center mt-1 space-x-4">
                <div className="flex items-center space-x-2">
                  <div className={`w-2 h-2 rounded-full ${autoRefresh ? 'bg-green-500' : 'bg-gray-400'}`}></div>
                  <span className="text-xs text-gray-500">
                    {autoRefresh ? '自动刷新已开启' : '自动刷新已关闭'}
                  </span>
                </div>
                <button
                  onClick={() => setAutoRefresh(!autoRefresh)}
                  className="text-xs text-blue-600 hover:text-blue-800"
                >
                  {autoRefresh ? '关闭自动刷新' : '开启自动刷新'}
                </button>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <button
                onClick={manualRefresh}
                disabled={isRefreshing}
                className="flex items-center space-x-1 bg-gray-100 hover:bg-gray-200 disabled:bg-gray-50 text-gray-700 px-3 py-2 rounded-md text-sm font-medium"
              >
                <ArrowPathIcon className={`w-4 h-4 ${isRefreshing ? 'animate-spin' : ''}`} />
                <span>{isRefreshing ? '刷新中...' : '刷新'}</span>
              </button>
              <Link
                href={`/orders/${order.id}`}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium"
              >
                查看订单
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* 消息区域 */}
      <div className="flex-1 overflow-hidden">
        <div className="max-w-4xl mx-auto h-full flex flex-col">
          <div className="flex-1 overflow-y-auto p-4 space-y-4">
            {messages.length === 0 ? (
              <div className="text-center py-12">
                <div className="text-gray-500">还没有消息，开始对话吧！</div>
              </div>
            ) : (
              messages.map((message) => {
                const isOwnMessage = message.sender.id === session?.user?.id
                return (
                  <MessageRenderer
                    key={message.id}
                    message={message}
                    isOwnMessage={isOwnMessage}
                    formatTime={formatTime}
                  />
                )
              })
            )}

            <div ref={messagesEndRef} />
          </div>

          {/* 消息输入区域 */}
          <div className="bg-white border-t p-4">
            {/* 文件上传区域 */}
            {showFileUpload && (
              <div className="mb-4 p-4 bg-gray-50 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <h3 className="text-sm font-medium text-gray-700">上传文件</h3>
                  <button
                    onClick={() => setShowFileUpload(false)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
                <FileUpload
                  orderId={orderId}
                  onFileUploaded={handleFileUploaded}
                  disabled={isSending}
                />
              </div>
            )}

            <form onSubmit={handleSendMessage} className="flex space-x-2">
              <button
                type="button"
                onClick={() => setShowFileUpload(!showFileUpload)}
                disabled={isSending}
                className="flex-shrink-0 p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <PaperClipIcon className="w-5 h-5" />
              </button>
              <input
                type="text"
                value={newMessage}
                onChange={handleInputChange}
                placeholder="输入消息..."
                className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                maxLength={1000}
                disabled={isSending}
              />
              <button
                type="submit"
                disabled={!newMessage.trim() || isSending}
                className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md text-sm font-medium disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isSending ? '发送中...' : '发送'}
              </button>
            </form>
            <div className="flex justify-between items-center text-xs text-gray-500 mt-1">
              <span>{newMessage.length}/1000 字符</span>
              <div className="flex items-center space-x-2">
                <span>💬 HTTP消息传输 (非实时)</span>
                {autoRefresh && (
                  <span>• 每3秒自动刷新</span>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
