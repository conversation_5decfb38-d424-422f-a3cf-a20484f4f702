const { PrismaClient } = require('@prisma/client')
const bcrypt = require('bcryptjs')

const prisma = new PrismaClient()

async function createFreshTestData() {
  try {
    console.log('🧹 清理现有数据...')
    
    // 删除所有现有数据（按依赖关系顺序，只删除存在的表）
    try {
      await prisma.message.deleteMany({})
      console.log('  ✓ 删除消息记录')
    } catch (e) { console.log('  - 跳过消息表') }

    try {
      await prisma.review.deleteMany({})
      console.log('  ✓ 删除评价记录')
    } catch (e) { console.log('  - 跳过评价表') }

    try {
      await prisma.orderItem.deleteMany({})
      console.log('  ✓ 删除订单项')
    } catch (e) { console.log('  - 跳过订单项表') }

    try {
      await prisma.order.deleteMany({})
      console.log('  ✓ 删除订单记录')
    } catch (e) { console.log('  - 跳过订单表') }

    try {
      await prisma.demandOffer.deleteMany({})
      console.log('  ✓ 删除需求报价')
    } catch (e) { console.log('  - 跳过需求报价表') }

    try {
      await prisma.demand.deleteMany({})
      console.log('  ✓ 删除需求记录')
    } catch (e) { console.log('  - 跳过需求表') }

    try {
      await prisma.productVariant.deleteMany({})
      console.log('  ✓ 删除商品变体')
    } catch (e) { console.log('  - 跳过商品变体表') }

    try {
      await prisma.product.deleteMany({})
      console.log('  ✓ 删除商品记录')
    } catch (e) { console.log('  - 跳过商品表') }

    try {
      await prisma.fundTransaction.deleteMany({})
      console.log('  ✓ 删除资金交易')
    } catch (e) { console.log('  - 跳过资金交易表') }

    try {
      await prisma.depositRecord.deleteMany({})
      console.log('  ✓ 删除充值记录')
    } catch (e) { console.log('  - 跳过充值记录表') }

    try {
      await prisma.depositOperation.deleteMany({})
      console.log('  ✓ 删除充值操作')
    } catch (e) { console.log('  - 跳过充值操作表') }

    try {
      await prisma.withdrawal.deleteMany({})
      console.log('  ✓ 删除提现记录')
    } catch (e) { console.log('  - 跳过提现记录表') }

    try {
      await prisma.giftCardTransaction.deleteMany({})
      console.log('  ✓ 删除礼品卡交易')
    } catch (e) { console.log('  - 跳过礼品卡交易表') }

    try {
      await prisma.giftCard.deleteMany({})
      console.log('  ✓ 删除礼品卡')
    } catch (e) { console.log('  - 跳过礼品卡表') }

    try {
      await prisma.giftCardProduct.deleteMany({})
      console.log('  ✓ 删除礼品卡商品')
    } catch (e) { console.log('  - 跳过礼品卡商品表') }

    try {
      await prisma.redemptionCodeUsage.deleteMany({})
      console.log('  ✓ 删除兑换码使用记录')
    } catch (e) { console.log('  - 跳过兑换码使用记录表') }

    try {
      await prisma.redemptionCode.deleteMany({})
      console.log('  ✓ 删除兑换码')
    } catch (e) { console.log('  - 跳过兑换码表') }

    try {
      await prisma.rewardCoupon.deleteMany({})
      console.log('  ✓ 删除奖励券')
    } catch (e) { console.log('  - 跳过奖励券表') }

    try {
      await prisma.withdrawalVoucher.deleteMany({})
      console.log('  ✓ 删除提现券')
    } catch (e) { console.log('  - 跳过提现券表') }

    try {
      await prisma.account.deleteMany({})
      console.log('  ✓ 删除账户记录')
    } catch (e) { console.log('  - 跳过账户表') }

    try {
      await prisma.user.deleteMany({})
      console.log('  ✓ 删除用户记录')
    } catch (e) { console.log('  - 跳过用户表') }
    
    console.log('✅ 现有数据已清理')

    console.log('👤 创建测试账号...')
    
    // 创建管理员账号
    const adminUser = await prisma.user.create({
      data: {
        name: '系统管理员',
        email: '<EMAIL>',
        password: await bcrypt.hash('admin123', 10),
        role: 'ADMIN',
        status: 'ACTIVE',
        creditScore: 100,
        depositBalance: 0,
        isGuarantor: true,
        city: '北京',
        district: '朝阳区',
        emailVerified: new Date()
      }
    })
    console.log(`✅ 管理员账号创建成功: ${adminUser.email}`)

    // 创建测试买家账号
    const buyerUser = await prisma.user.create({
      data: {
        name: '测试买家',
        email: '<EMAIL>',
        password: await bcrypt.hash('buyer123', 10),
        role: 'USER',
        status: 'ACTIVE',
        creditScore: 85,
        depositBalance: 1000.00,
        isGuarantor: false,
        city: '上海',
        district: '浦东新区',
        emailVerified: new Date()
      }
    })
    console.log(`✅ 买家账号创建成功: ${buyerUser.email}`)

    // 创建测试卖家账号
    const sellerUser = await prisma.user.create({
      data: {
        name: '测试卖家',
        email: '<EMAIL>',
        password: await bcrypt.hash('seller123', 10),
        role: 'USER',
        status: 'ACTIVE',
        creditScore: 92,
        depositBalance: 2000.00,
        isGuarantor: true,
        city: '深圳',
        district: '南山区',
        emailVerified: new Date()
      }
    })
    console.log(`✅ 卖家账号创建成功: ${sellerUser.email}`)

    console.log('📱 创建测试商品...')
    
    // 创建测试商品
    const testProduct = await prisma.product.create({
      data: {
        title: 'iPhone 15 Pro Max 256GB',
        description: '全新未拆封的iPhone 15 Pro Max，256GB存储容量，深空黑色。包装完整，配件齐全。支持面交验货，确保正品。',
        price: 8999.00,
        category: '数码产品',
        condition: 'NEW',
        status: 'AVAILABLE',
        reviewStatus: 'APPROVED',
        sellerId: sellerUser.id,
        city: '深圳',
        district: '南山区',
        address: '科技园地铁站附近',
        latitude: 22.5431,
        longitude: 113.9344,
        images: JSON.stringify([
          'https://example.com/iphone15pro1.jpg',
          'https://example.com/iphone15pro2.jpg'
        ]),
        stock: 1,
        isLocationPublic: true,
        preferLocalTrade: true
      }
    })
    console.log(`✅ 测试商品创建成功: ${testProduct.title}`)

    // 创建一些测试交易记录来生成统计数据
    console.log('💰 创建测试交易记录...')
    
    // 为买家创建一些交易记录
    await prisma.fundTransaction.createMany({
      data: [
        {
          userId: buyerUser.id,
          type: 'DEPOSIT',
          amount: 1000.00,
          description: '初始充值',
          relatedId: null,
          metadata: { method: 'USDT' }
        },
        {
          userId: buyerUser.id,
          type: 'PURCHASE',
          amount: -299.00,
          description: '购买商品 - 小米手机',
          relatedId: null,
          metadata: { productId: 'test_product_1' }
        },
        {
          userId: buyerUser.id,
          type: 'GUARANTEE',
          amount: -50.00,
          description: '担保金冻结',
          relatedId: null,
          metadata: { orderId: 'test_order_1' }
        }
      ]
    })

    // 为卖家创建一些交易记录
    await prisma.fundTransaction.createMany({
      data: [
        {
          userId: sellerUser.id,
          type: 'DEPOSIT',
          amount: 2000.00,
          description: '初始充值',
          relatedId: null,
          metadata: { method: 'USDT' }
        },
        {
          userId: sellerUser.id,
          type: 'SALE',
          amount: 299.00,
          description: '商品销售收入 - 小米手机',
          relatedId: null,
          metadata: { productId: 'test_product_1' }
        },
        {
          userId: sellerUser.id,
          type: 'COMMISSION',
          amount: 15.00,
          description: '担保服务佣金',
          relatedId: null,
          metadata: { orderId: 'test_order_1' }
        }
      ]
    })

    // 创建一些测试订单来生成履约率数据
    console.log('📦 创建测试订单...')
    
    const testOrder1 = await prisma.order.create({
      data: {
        orderNumber: 'ORD' + Date.now() + '001',
        status: 'COMPLETED',
        totalAmount: 299.00,
        productPrice: 299.00,
        shippingFee: 0,
        platformFee: 0,
        buyerId: buyerUser.id,
        sellerId: sellerUser.id,
        productId: testProduct.id,
        paymentMethod: 'BALANCE',
        paymentConfirmed: true,
        metadata: {
          completedAt: new Date().toISOString(),
          productTitle: '小米手机 - 已完成订单'
        }
      }
    })

    const testOrder2 = await prisma.order.create({
      data: {
        orderNumber: 'ORD' + Date.now() + '002',
        status: 'DELIVERED',
        totalAmount: 199.00,
        productPrice: 199.00,
        shippingFee: 0,
        platformFee: 0,
        buyerId: buyerUser.id,
        sellerId: sellerUser.id,
        productId: testProduct.id,
        paymentMethod: 'BALANCE',
        paymentConfirmed: true,
        metadata: {
          deliveredAt: new Date().toISOString(),
          productTitle: '华为手机 - 已发货订单'
        }
      }
    })

    console.log('✅ 测试数据创建完成!')
    console.log('\n📋 账号信息:')
    console.log('管理员账号:')
    console.log(`  邮箱: ${adminUser.email}`)
    console.log(`  密码: admin123`)
    console.log(`  角色: 管理员`)
    
    console.log('\n买家账号:')
    console.log(`  邮箱: ${buyerUser.email}`)
    console.log(`  密码: buyer123`)
    console.log(`  余额: ${buyerUser.depositBalance} USDT`)
    
    console.log('\n卖家账号:')
    console.log(`  邮箱: ${sellerUser.email}`)
    console.log(`  密码: seller123`)
    console.log(`  余额: ${sellerUser.depositBalance} USDT`)
    console.log(`  中间人: 是`)
    
    console.log('\n📱 商品信息:')
    console.log(`  商品: ${testProduct.title}`)
    console.log(`  价格: ¥${testProduct.price}`)
    console.log(`  卖家: ${sellerUser.name}`)
    
    console.log('\n🔗 访问链接:')
    console.log('用户充值页面: http://localhost:3001/deposit')
    console.log('管理员后台: http://localhost:3001/admin/deposits')
    console.log('商品列表: http://localhost:3001/products')
    
    console.log('\n💡 使用说明:')
    console.log('1. 使用买家账号登录测试充值功能')
    console.log('2. 使用管理员账号登录审核充值申请')
    console.log('3. 查看真实的统计数据（信誉等级、履约率等）')

  } catch (error) {
    console.error('❌ 创建测试数据失败:', error)
  } finally {
    await prisma.$disconnect()
  }
}

// 运行脚本
createFreshTestData()
