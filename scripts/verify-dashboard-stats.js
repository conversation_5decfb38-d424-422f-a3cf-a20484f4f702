const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function verifyDashboardStats() {
  try {
    console.log('📊 验证管理员仪表板统计数据')
    console.log('=' .repeat(50))
    
    // 1. 验证用户统计
    console.log('\n1. 用户统计验证...')
    
    const totalUsersAll = await prisma.user.count()
    const totalUsersExcludingDeleted = await prisma.user.count({
      where: {
        NOT: {
          name: {
            contains: '已删除用户#'
          }
        }
      }
    })
    const deletedUsers = await prisma.user.count({
      where: {
        name: {
          contains: '已删除用户#'
        }
      }
    })
    
    console.log(`   数据库中总用户数: ${totalUsersAll}`)
    console.log(`   实际注册用户数: ${totalUsersExcludingDeleted}`)
    console.log(`   已删除用户数: ${deletedUsers}`)
    console.log(`   ✅ 仪表板应显示: ${totalUsersExcludingDeleted} 个用户`)
    
    // 2. 验证商品统计
    console.log('\n2. 商品统计验证...')
    
    const totalProductsAll = await prisma.product.count()
    const totalProductsExcludingDeleted = await prisma.product.count({
      where: {
        seller: {
          NOT: {
            name: {
              contains: '已删除用户#'
            }
          }
        }
      }
    })
    const deletedUserProducts = await prisma.product.count({
      where: {
        seller: {
          name: {
            contains: '已删除用户#'
          }
        }
      }
    })
    
    console.log(`   数据库中总商品数: ${totalProductsAll}`)
    console.log(`   活跃用户商品数: ${totalProductsExcludingDeleted}`)
    console.log(`   已删除用户商品数: ${deletedUserProducts}`)
    console.log(`   ✅ 仪表板应显示: ${totalProductsExcludingDeleted} 个商品`)
    
    // 3. 验证订单统计
    console.log('\n3. 订单统计验证...')
    
    const totalOrders = await prisma.order.count()
    const pendingOrders = await prisma.order.count({
      where: {
        status: {
          in: ['PENDING_PAYMENT', 'PAID', 'SHIPPED']
        }
      }
    })
    const pendingPayments = await prisma.order.count({
      where: {
        status: 'PAID',
        paymentConfirmed: false
      }
    })
    
    console.log(`   总订单数: ${totalOrders}`)
    console.log(`   待处理订单: ${pendingOrders}`)
    console.log(`   待确认付款: ${pendingPayments}`)
    console.log(`   ✅ 订单统计包含所有历史订单（包括已删除用户的订单）`)
    
    // 4. 验证收入统计
    console.log('\n4. 收入统计验证...')
    
    const completedOrders = await prisma.order.findMany({
      where: {
        status: 'COMPLETED'
      },
      select: {
        platformFee: true
      }
    })
    
    const totalRevenue = completedOrders.reduce((sum, order) => sum + order.platformFee, 0)
    
    console.log(`   已完成订单数: ${completedOrders.length}`)
    console.log(`   平台总收入: ¥${totalRevenue.toFixed(2)}`)
    console.log(`   ✅ 收入统计包含所有历史收入`)
    
    // 5. 详细用户分析
    console.log('\n5. 详细用户分析...')
    
    const activeUsers = await prisma.user.findMany({
      where: {
        status: 'ACTIVE',
        NOT: {
          name: {
            contains: '已删除用户#'
          }
        }
      },
      select: {
        name: true,
        email: true,
        role: true
      }
    })
    
    const anonymousUsers = await prisma.user.findMany({
      where: {
        name: {
          contains: '已删除用户#'
        }
      },
      select: {
        name: true,
        email: true,
        createdAt: true
      }
    })
    
    console.log(`   活跃用户列表:`)
    activeUsers.forEach((user, index) => {
      console.log(`     ${index + 1}. ${user.name || user.email} (${user.role})`)
    })
    
    console.log(`   匿名用户列表:`)
    anonymousUsers.forEach((user, index) => {
      console.log(`     ${index + 1}. ${user.name} - ${user.createdAt.toLocaleDateString()}`)
    })
    
    // 6. 统计数据对比
    console.log('\n6. 统计数据对比...')
    console.log('=' .repeat(40))
    
    console.log('📊 仪表板统计（修改后）:')
    console.log(`   总用户数: ${totalUsersExcludingDeleted} （排除已删除用户）`)
    console.log(`   总商品数: ${totalProductsExcludingDeleted} （排除已删除用户商品）`)
    console.log(`   总订单数: ${totalOrders} （包含所有历史订单）`)
    console.log(`   待处理订单: ${pendingOrders}`)
    console.log(`   待确认付款: ${pendingPayments}`)
    console.log(`   平台收入: ¥${totalRevenue.toFixed(2)}`)
    
    console.log('\n📈 数据库实际情况:')
    console.log(`   数据库总用户: ${totalUsersAll}`)
    console.log(`   - 活跃用户: ${totalUsersExcludingDeleted}`)
    console.log(`   - 匿名用户: ${deletedUsers}`)
    console.log(`   数据库总商品: ${totalProductsAll}`)
    console.log(`   - 活跃用户商品: ${totalProductsExcludingDeleted}`)
    console.log(`   - 匿名用户商品: ${deletedUserProducts}`)
    
    console.log('\n✅ 统计逻辑验证:')
    console.log('• 用户统计：只计算真实注册用户，不包括匿名用户 ✅')
    console.log('• 商品统计：只计算活跃用户的商品，不包括已删除用户商品 ✅')
    console.log('• 订单统计：包含所有历史订单，维护业务完整性 ✅')
    console.log('• 收入统计：包含所有历史收入，确保财务准确性 ✅')
    
  } catch (error) {
    console.error('验证失败:', error)
  } finally {
    await prisma.$disconnect()
  }
}

verifyDashboardStats()
  .then(() => {
    console.log('\n🎉 仪表板统计数据验证完成！')
    console.log('现在管理员仪表板显示的用户数量是真实的注册用户数量，')
    console.log('不包括已删除的匿名用户，数据更加准确和有意义！')
    process.exit(0)
  })
  .catch((error) => {
    console.error('\n💥 验证失败:', error)
    process.exit(1)
  })
