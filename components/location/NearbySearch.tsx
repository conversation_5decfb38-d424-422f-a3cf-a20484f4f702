'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Slider } from '@/components/ui/slider'
import {
  MapPin,
  Navigation,
  Filter,
  SortAsc,
  SortDesc,
  Loader2,
  RefreshCw,
  Map,
  List,
  Target
} from 'lucide-react'
import MapView from './MapView'

interface NearbySearchProps {
  type: 'product' | 'demand'
  onResults: (results: any) => void
  initialLocation?: {
    latitude: number
    longitude: number
  }
}

interface SearchFilters {
  radius: number
  category?: string
  demandType?: string
  sortBy: 'distance' | 'price' | 'createdAt'
  sortOrder: 'asc' | 'desc'
}

export default function NearbySearch({ 
  type, 
  onResults, 
  initialLocation 
}: NearbySearchProps) {
  const [location, setLocation] = useState(initialLocation)
  const [filters, setFilters] = useState<SearchFilters>({
    radius: 5,
    sortBy: 'distance',
    sortOrder: 'asc'
  })
  const [isSearching, setIsSearching] = useState(false)
  const [isGettingLocation, setIsGettingLocation] = useState(false)
  const [results, setResults] = useState<any>(null)
  const [viewMode, setViewMode] = useState<'list' | 'map'>('list')

  // 预设半径选项
  const radiusOptions = [
    { value: 1, label: '1km' },
    { value: 3, label: '3km' },
    { value: 5, label: '5km' },
    { value: 10, label: '10km' },
    { value: 20, label: '20km' },
    { value: 50, label: '50km' }
  ]

  // 分类选项
  const categoryOptions = [
    { value: '', label: '全部分类' },
    { value: 'electronics', label: '数码电子' },
    { value: 'fashion', label: '服装配饰' },
    { value: 'home', label: '家居用品' },
    { value: 'books', label: '图书文具' },
    { value: 'sports', label: '运动户外' },
    { value: 'beauty', label: '美妆护肤' },
    { value: 'food', label: '食品饮料' },
    { value: 'other', label: '其他' }
  ]

  // 需求类型选项
  const demandTypeOptions = [
    { value: '', label: '全部类型' },
    { value: 'BUY', label: '求购' },
    { value: 'SELL', label: '出售' },
    { value: 'SERVICE', label: '服务' },
    { value: 'EXCHANGE', label: '交换' }
  ]

  useEffect(() => {
    if (location) {
      performSearch()
    }
  }, [location, filters])

  // 获取当前位置
  const getCurrentLocation = async () => {
    setIsGettingLocation(true)

    try {
      const position = await new Promise<GeolocationPosition>((resolve, reject) => {
        navigator.geolocation.getCurrentPosition(
          resolve,
          reject,
          {
            enableHighAccuracy: true,
            timeout: 10000,
            maximumAge: 300000
          }
        )
      })

      const newLocation = {
        latitude: position.coords.latitude,
        longitude: position.coords.longitude
      }

      setLocation(newLocation)

    } catch (error) {
      console.error('获取位置失败:', error)
      alert('获取位置失败，请检查定位权限')
    } finally {
      setIsGettingLocation(false)
    }
  }

  // 执行搜索
  const performSearch = async () => {
    if (!location) return

    setIsSearching(true)

    try {
      const params = new URLSearchParams({
        type,
        lat: location.latitude.toString(),
        lng: location.longitude.toString(),
        radius: filters.radius.toString(),
        sortBy: filters.sortBy,
        sortOrder: filters.sortOrder,
        limit: '20'
      })

      if (filters.category) {
        params.append('category', filters.category)
      }

      if (filters.demandType) {
        params.append('demandType', filters.demandType)
      }

      const response = await fetch(`/api/location/nearby?${params}`)
      
      if (response.ok) {
        const data = await response.json()
        setResults(data)
        onResults(data)
      } else {
        console.error('搜索失败')
      }

    } catch (error) {
      console.error('搜索失败:', error)
    } finally {
      setIsSearching(false)
    }
  }

  // 更新筛选条件
  const updateFilter = (key: keyof SearchFilters, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }))
  }

  return (
    <div className="space-y-4">
      {/* 位置和搜索控制 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Target className="h-5 w-5" />
              <span>附近搜索</span>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setViewMode(viewMode === 'list' ? 'map' : 'list')}
              >
                {viewMode === 'list' ? <Map className="h-4 w-4" /> : <List className="h-4 w-4" />}
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={performSearch}
                disabled={isSearching || !location}
              >
                {isSearching ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <RefreshCw className="h-4 w-4" />
                )}
              </Button>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* 位置状态 */}
          {location ? (
            <div className="flex items-center justify-between p-3 bg-green-50 border border-green-200 rounded-lg">
              <div className="flex items-center space-x-2">
                <MapPin className="h-4 w-4 text-green-600" />
                <span className="text-sm text-green-800">
                  位置: {location.latitude.toFixed(4)}, {location.longitude.toFixed(4)}
                </span>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={getCurrentLocation}
                disabled={isGettingLocation}
              >
                {isGettingLocation ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <Navigation className="h-4 w-4" />
                )}
              </Button>
            </div>
          ) : (
            <div className="text-center p-6 border-2 border-dashed border-gray-300 rounded-lg">
              <MapPin className="h-8 w-8 text-gray-400 mx-auto mb-2" />
              <p className="text-gray-500 mb-3">请先获取您的位置</p>
              <Button onClick={getCurrentLocation} disabled={isGettingLocation}>
                {isGettingLocation ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    获取位置中...
                  </>
                ) : (
                  <>
                    <Navigation className="h-4 w-4 mr-2" />
                    获取当前位置
                  </>
                )}
              </Button>
            </div>
          )}

          {/* 搜索筛选器 */}
          {location && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {/* 搜索半径 */}
              <div className="space-y-2">
                <label className="text-sm font-medium">搜索半径</label>
                <div className="space-y-2">
                  <Slider
                    value={[filters.radius]}
                    onValueChange={(value) => updateFilter('radius', value[0])}
                    max={50}
                    min={1}
                    step={1}
                  />
                  <div className="flex justify-between text-xs text-gray-500">
                    <span>1km</span>
                    <span className="font-medium">{filters.radius}km</span>
                    <span>50km</span>
                  </div>
                </div>
              </div>

              {/* 分类筛选 */}
              {type === 'product' && (
                <div className="space-y-2">
                  <label className="text-sm font-medium">商品分类</label>
                  <Select
                    value={filters.category || ''}
                    onValueChange={(value) => updateFilter('category', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="选择分类" />
                    </SelectTrigger>
                    <SelectContent>
                      {categoryOptions.map(option => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              )}

              {/* 需求类型筛选 */}
              {type === 'demand' && (
                <div className="space-y-2">
                  <label className="text-sm font-medium">需求类型</label>
                  <Select
                    value={filters.demandType || ''}
                    onValueChange={(value) => updateFilter('demandType', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="选择类型" />
                    </SelectTrigger>
                    <SelectContent>
                      {demandTypeOptions.map(option => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              )}

              {/* 排序方式 */}
              <div className="space-y-2">
                <label className="text-sm font-medium">排序方式</label>
                <div className="flex space-x-2">
                  <Select
                    value={filters.sortBy}
                    onValueChange={(value) => updateFilter('sortBy', value)}
                  >
                    <SelectTrigger className="flex-1">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="distance">距离</SelectItem>
                      <SelectItem value="price">{type === 'product' ? '价格' : '预算'}</SelectItem>
                      <SelectItem value="createdAt">时间</SelectItem>
                    </SelectContent>
                  </Select>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => updateFilter('sortOrder', filters.sortOrder === 'asc' ? 'desc' : 'asc')}
                  >
                    {filters.sortOrder === 'asc' ? (
                      <SortAsc className="h-4 w-4" />
                    ) : (
                      <SortDesc className="h-4 w-4" />
                    )}
                  </Button>
                </div>
              </div>
            </div>
          )}

          {/* 搜索结果摘要 */}
          {results && (
            <div className="flex items-center justify-between p-3 bg-blue-50 border border-blue-200 rounded-lg">
              <div className="flex items-center space-x-4">
                <Badge variant="secondary">
                  找到 {results.total} 个结果
                </Badge>
                <Badge variant="outline">
                  搜索半径 {results.metadata.searchRadiusText}
                </Badge>
              </div>
              {results.hasMore && (
                <Badge variant="outline">
                  还有更多结果
                </Badge>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* 地图视图 */}
      {viewMode === 'map' && results && location && (
        <MapView
          center={location}
          items={(results[type === 'product' ? 'products' : 'demands'] || []).map((result: any) => ({
            id: result.item.id,
            title: result.item.title,
            latitude: result.item.latitude,
            longitude: result.item.longitude,
            price: result.item.price,
            budget: result.item.budget,
            type: type,
            distance: result.distance,
            distanceText: result.distanceText,
            item: result.item
          }))}
          searchRadius={filters.radius}
          onItemClick={(item) => {
            console.log('地图项目点击:', item)
            // 这里可以添加跳转到详情页的逻辑
          }}
        />
      )}
    </div>
  )
}
