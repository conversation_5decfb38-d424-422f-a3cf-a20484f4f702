'use client'

import { useState, useEffect, useRef } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  MapPin, 
  Navigation, 
  ZoomIn,
  ZoomOut,
  Layers,
  Target,
  Info,
  X,
  AlertCircle
} from 'lucide-react'

interface AMapViewProps {
  center: {
    latitude: number
    longitude: number
  }
  items: Array<{
    id: string
    title: string
    latitude: number
    longitude: number
    price?: number
    budget?: number
    type: 'product' | 'demand'
    distance: number
    distanceText: string
    item: any
  }>
  onItemClick?: (item: any) => void
  searchRadius?: number
  className?: string
  apiKey?: string
}

// 高德地图类型定义
declare global {
  interface Window {
    AMap: any
    AMapLoader: any
  }
}

export default function AMapView({
  center,
  items,
  onItemClick,
  searchRadius = 5,
  className = '',
  apiKey = process.env.NEXT_PUBLIC_AMAP_API_KEY
}: AMapViewProps) {
  const mapRef = useRef<HTMLDivElement>(null)
  const [map, setMap] = useState<any>(null)
  const [selectedItem, setSelectedItem] = useState<any>(null)
  const [mapLoaded, setMapLoaded] = useState(false)
  const [mapError, setMapError] = useState<string>('')
  const [markers, setMarkers] = useState<any[]>([])
  const [circle, setCircle] = useState<any>(null)

  // 加载高德地图
  useEffect(() => {
    if (!apiKey) {
      setMapError('缺少高德地图API密钥')
      return
    }

    loadAMap()
  }, [apiKey])

  // 更新地图内容
  useEffect(() => {
    if (map && mapLoaded) {
      updateMapContent()
    }
  }, [map, mapLoaded, center, items, searchRadius])

  const loadAMap = async () => {
    try {
      // 检查是否已经加载了高德地图
      if (window.AMap) {
        initializeMap()
        return
      }

      // 动态加载高德地图API
      const script = document.createElement('script')
      script.src = `https://webapi.amap.com/maps?v=2.0&key=${apiKey}&plugin=AMap.Scale,AMap.ToolBar,AMap.ControlBar`
      script.async = true
      
      script.onload = () => {
        initializeMap()
      }
      
      script.onerror = () => {
        setMapError('高德地图加载失败')
      }
      
      document.head.appendChild(script)

    } catch (error) {
      console.error('加载高德地图失败:', error)
      setMapError('地图初始化失败')
    }
  }

  const initializeMap = () => {
    if (!mapRef.current || !window.AMap) return

    try {
      const mapInstance = new window.AMap.Map(mapRef.current, {
        zoom: 13,
        center: [center.longitude, center.latitude],
        mapStyle: 'amap://styles/normal',
        features: ['bg', 'road', 'building', 'point'],
        viewMode: '2D'
      })

      // 添加地图控件
      mapInstance.addControl(new window.AMap.Scale())
      mapInstance.addControl(new window.AMap.ToolBar({
        position: {
          top: '10px',
          right: '10px'
        }
      }))

      setMap(mapInstance)
      setMapLoaded(true)

    } catch (error) {
      console.error('地图初始化失败:', error)
      setMapError('地图初始化失败')
    }
  }

  const updateMapContent = () => {
    if (!map || !window.AMap) return

    // 清除现有标记和圆圈
    clearMapContent()

    // 设置地图中心
    map.setCenter([center.longitude, center.latitude])

    // 添加搜索范围圆圈
    const searchCircle = new window.AMap.Circle({
      center: [center.longitude, center.latitude],
      radius: searchRadius * 1000, // 转换为米
      fillColor: '#3b82f6',
      fillOpacity: 0.1,
      strokeColor: '#3b82f6',
      strokeWeight: 2,
      strokeOpacity: 0.8,
      strokeStyle: 'dashed'
    })
    
    map.add(searchCircle)
    setCircle(searchCircle)

    // 添加中心标记
    const centerMarker = new window.AMap.Marker({
      position: [center.longitude, center.latitude],
      icon: new window.AMap.Icon({
        image: 'data:image/svg+xml;base64,' + btoa(`
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="12" cy="12" r="8" fill="#ef4444" stroke="#ffffff" stroke-width="2"/>
            <circle cx="12" cy="12" r="3" fill="#ffffff"/>
          </svg>
        `),
        size: new window.AMap.Size(24, 24),
        imageOffset: new window.AMap.Pixel(-12, -12)
      }),
      title: '搜索中心',
      offset: new window.AMap.Pixel(-12, -12)
    })

    map.add(centerMarker)

    // 添加商品/需求标记
    const newMarkers = items.map(item => {
      const markerIcon = item.type === 'product' 
        ? createProductIcon() 
        : createDemandIcon()

      const marker = new window.AMap.Marker({
        position: [item.longitude, item.latitude],
        icon: markerIcon,
        title: item.title,
        offset: new window.AMap.Pixel(-12, -12)
      })

      // 添加点击事件
      marker.on('click', () => {
        setSelectedItem(item)
        onItemClick?.(item)
      })

      // 添加信息窗口
      const infoWindow = new window.AMap.InfoWindow({
        content: createInfoWindowContent(item),
        offset: new window.AMap.Pixel(0, -30)
      })

      marker.on('mouseover', () => {
        infoWindow.open(map, marker.getPosition())
      })

      marker.on('mouseout', () => {
        infoWindow.close()
      })

      map.add(marker)
      return marker
    })

    setMarkers([centerMarker, ...newMarkers])

    // 自适应显示所有标记
    if (items.length > 0) {
      const bounds = new window.AMap.Bounds()
      bounds.extend([center.longitude, center.latitude])
      items.forEach(item => {
        bounds.extend([item.longitude, item.latitude])
      })
      map.setBounds(bounds, false, [50, 50, 50, 50])
    }
  }

  const clearMapContent = () => {
    if (markers.length > 0) {
      map.remove(markers)
      setMarkers([])
    }
    if (circle) {
      map.remove(circle)
      setCircle(null)
    }
  }

  const createProductIcon = () => {
    return new window.AMap.Icon({
      image: 'data:image/svg+xml;base64,' + btoa(`
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <circle cx="12" cy="12" r="10" fill="#3b82f6" stroke="#ffffff" stroke-width="2"/>
          <text x="12" y="16" text-anchor="middle" fill="white" font-size="12">🛍️</text>
        </svg>
      `),
      size: new window.AMap.Size(24, 24),
      imageOffset: new window.AMap.Pixel(-12, -12)
    })
  }

  const createDemandIcon = () => {
    return new window.AMap.Icon({
      image: 'data:image/svg+xml;base64,' + btoa(`
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <circle cx="12" cy="12" r="10" fill="#10b981" stroke="#ffffff" stroke-width="2"/>
          <text x="12" y="16" text-anchor="middle" fill="white" font-size="12">📋</text>
        </svg>
      `),
      size: new window.AMap.Size(24, 24),
      imageOffset: new window.AMap.Pixel(-12, -12)
    })
  }

  const createInfoWindowContent = (item: any) => {
    return `
      <div style="padding: 8px; min-width: 200px;">
        <div style="font-weight: bold; margin-bottom: 4px;">${item.title}</div>
        <div style="color: #666; font-size: 12px; margin-bottom: 4px;">
          ${item.type === 'product' ? '价格' : '预算'}: ¥${(item.price || item.budget || 0).toFixed(2)}
        </div>
        <div style="color: #666; font-size: 12px;">
          距离: ${item.distanceText}
        </div>
      </div>
    `
  }

  const handleRecenter = () => {
    if (map) {
      map.setCenter([center.longitude, center.latitude])
      map.setZoom(13)
    }
  }

  if (mapError) {
    return (
      <Alert className="mb-4">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          {mapError}
          {!apiKey && (
            <div className="mt-2">
              <p className="text-sm">请在环境变量中配置 NEXT_PUBLIC_AMAP_API_KEY</p>
              <p className="text-xs text-gray-500">
                获取API密钥: <a href="https://console.amap.com/" target="_blank" className="underline">高德开放平台</a>
              </p>
            </div>
          )}
        </AlertDescription>
      </Alert>
    )
  }

  return (
    <div className={`relative ${className}`}>
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center space-x-2">
              <MapPin className="h-5 w-5" />
              <span>高德地图视图</span>
            </CardTitle>
            <div className="flex items-center space-x-2">
              <Badge variant="outline">
                {items.length} 个结果
              </Badge>
              <Badge variant="outline">
                {searchRadius}km 范围
              </Badge>
              <Button
                variant="outline"
                size="sm"
                onClick={handleRecenter}
              >
                <Navigation className="h-4 w-4 mr-1" />
                重新定位
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent className="p-0">
          <div className="relative">
            {/* 地图容器 */}
            <div 
              ref={mapRef}
              className="h-96 w-full"
              style={{ minHeight: '400px' }}
            />

            {/* 加载状态 */}
            {!mapLoaded && !mapError && (
              <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center">
                <div className="text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-2"></div>
                  <p className="text-sm text-gray-600">高德地图加载中...</p>
                </div>
              </div>
            )}

            {/* 选中项目详情 */}
            {selectedItem && (
              <div className="absolute bottom-4 left-4 right-4">
                <Card className="shadow-lg">
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-2">
                          <Badge variant={selectedItem.type === 'product' ? 'default' : 'secondary'}>
                            {selectedItem.type === 'product' ? '商品' : '需求'}
                          </Badge>
                          <Badge variant="outline">
                            {selectedItem.distanceText}
                          </Badge>
                        </div>
                        <h4 className="font-medium text-sm mb-1">{selectedItem.title}</h4>
                        <p className="text-sm text-gray-600">
                          {selectedItem.type === 'product' ? '价格' : '预算'}: 
                          <span className="font-medium ml-1">
                            ¥{(selectedItem.price || selectedItem.budget || 0).toFixed(2)}
                          </span>
                        </p>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setSelectedItem(null)}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* 地图说明 */}
      <Card className="mt-4">
        <CardContent className="p-4">
          <div className="flex items-start space-x-2">
            <Info className="h-4 w-4 text-blue-500 mt-0.5" />
            <div className="text-sm text-gray-600">
              <p className="font-medium mb-1">地图说明：</p>
              <div className="space-y-1">
                <p>• 🎯 红色圆点：搜索中心位置</p>
                <p>• 🛍️ 蓝色标记：商品位置</p>
                <p>• 📋 绿色标记：需求位置</p>
                <p>• 虚线圆圈：搜索范围</p>
                <p>• 点击标记查看详细信息</p>
                <p>• 鼠标悬停显示快速预览</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
