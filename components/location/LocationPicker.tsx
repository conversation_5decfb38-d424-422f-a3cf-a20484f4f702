'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Slider } from '@/components/ui/slider'
import { Badge } from '@/components/ui/badge'
import { 
  MapPin, 
  Navigation, 
  Search, 
  Settings,
  Eye,
  EyeOff,
  Loader2,
  CheckCircle,
  AlertCircle
} from 'lucide-react'

interface LocationData {
  latitude?: number
  longitude?: number
  address?: string
  city?: string
  district?: string
  locationRadius?: number
  isLocationPublic?: boolean
  preferLocalTrade?: boolean
}

interface LocationPickerProps {
  initialLocation?: LocationData
  onLocationChange: (location: LocationData) => void
  showPublicOption?: boolean
  showRadiusOption?: boolean
  showLocalTradeOption?: boolean
  placeholder?: string
}

export default function LocationPicker({
  initialLocation,
  onLocationChange,
  showPublicOption = true,
  showRadiusOption = true,
  showLocalTradeOption = false,
  placeholder = "请输入地址或允许定位"
}: LocationPickerProps) {
  const [location, setLocation] = useState<LocationData>(initialLocation || {})
  const [isGettingLocation, setIsGettingLocation] = useState(false)
  const [locationError, setLocationError] = useState<string>('')
  const [addressInput, setAddressInput] = useState(initialLocation?.address || '')

  useEffect(() => {
    if (initialLocation) {
      setLocation(initialLocation)
      setAddressInput(initialLocation.address || '')
    }
  }, [initialLocation])

  // 获取当前位置
  const getCurrentLocation = async () => {
    setIsGettingLocation(true)
    setLocationError('')

    try {
      if (!navigator.geolocation) {
        throw new Error('浏览器不支持地理定位')
      }

      const position = await new Promise<GeolocationPosition>((resolve, reject) => {
        navigator.geolocation.getCurrentPosition(
          resolve,
          reject,
          {
            enableHighAccuracy: true,
            timeout: 10000,
            maximumAge: 300000 // 5分钟缓存
          }
        )
      })

      const { latitude, longitude } = position.coords

      // 反向地理编码（这里使用模拟数据，实际应该调用地图API）
      const address = await reverseGeocode(latitude, longitude)

      const newLocation = {
        ...location,
        latitude,
        longitude,
        address: address.formatted_address,
        city: address.city,
        district: address.district
      }

      setLocation(newLocation)
      setAddressInput(address.formatted_address)
      onLocationChange(newLocation)

    } catch (error) {
      console.error('获取位置失败:', error)
      setLocationError(
        error instanceof Error 
          ? error.message 
          : '获取位置失败，请检查定位权限'
      )
    } finally {
      setIsGettingLocation(false)
    }
  }

  // 模拟反向地理编码
  const reverseGeocode = async (lat: number, lng: number) => {
    // 实际应该调用高德地图或百度地图API
    // 这里返回模拟数据
    return {
      formatted_address: `纬度${lat.toFixed(4)}, 经度${lng.toFixed(4)}`,
      city: '北京市',
      district: '海淀区'
    }
  }

  // 地址搜索
  const searchAddress = async () => {
    if (!addressInput.trim()) return

    try {
      // 这里应该调用地图API进行地址搜索
      // 模拟搜索结果
      const searchResult = {
        latitude: 39.9042 + (Math.random() - 0.5) * 0.1,
        longitude: 116.4074 + (Math.random() - 0.5) * 0.1,
        formatted_address: addressInput,
        city: '北京市',
        district: '朝阳区'
      }

      const newLocation = {
        ...location,
        latitude: searchResult.latitude,
        longitude: searchResult.longitude,
        address: searchResult.formatted_address,
        city: searchResult.city,
        district: searchResult.district
      }

      setLocation(newLocation)
      onLocationChange(newLocation)

    } catch (error) {
      console.error('地址搜索失败:', error)
      setLocationError('地址搜索失败')
    }
  }

  // 更新位置设置
  const updateLocationSetting = (key: keyof LocationData, value: any) => {
    const newLocation = { ...location, [key]: value }
    setLocation(newLocation)
    onLocationChange(newLocation)
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <MapPin className="h-5 w-5" />
          <span>位置设置</span>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* 地址输入和定位 */}
        <div className="space-y-2">
          <Label>地址信息</Label>
          <div className="flex space-x-2">
            <Input
              value={addressInput}
              onChange={(e) => setAddressInput(e.target.value)}
              placeholder={placeholder}
              className="flex-1"
            />
            <Button
              variant="outline"
              onClick={searchAddress}
              disabled={!addressInput.trim()}
            >
              <Search className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              onClick={getCurrentLocation}
              disabled={isGettingLocation}
            >
              {isGettingLocation ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Navigation className="h-4 w-4" />
              )}
            </Button>
          </div>
          {locationError && (
            <div className="flex items-center space-x-2 text-red-600 text-sm">
              <AlertCircle className="h-4 w-4" />
              <span>{locationError}</span>
            </div>
          )}
        </div>

        {/* 位置信息显示 */}
        {location.latitude && location.longitude && (
          <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
            <div className="flex items-center space-x-2 mb-2">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <span className="text-sm font-medium text-green-800">位置已设置</span>
            </div>
            <div className="text-xs text-gray-600 space-y-1">
              <div>地址: {location.address}</div>
              <div>坐标: {location.latitude.toFixed(4)}, {location.longitude.toFixed(4)}</div>
              {location.city && <div>城市: {location.city} {location.district}</div>}
            </div>
          </div>
        )}

        {/* 交易半径设置 */}
        {showRadiusOption && (
          <div className="space-y-2">
            <Label>期望交易半径</Label>
            <div className="px-3">
              <Slider
                value={[location.locationRadius || 5]}
                onValueChange={(value) => updateLocationSetting('locationRadius', value[0])}
                max={50}
                min={1}
                step={1}
                className="w-full"
              />
              <div className="flex justify-between text-xs text-gray-500 mt-1">
                <span>1km</span>
                <span className="font-medium">
                  {location.locationRadius || 5}km
                </span>
                <span>50km</span>
              </div>
            </div>
          </div>
        )}

        {/* 位置公开设置 */}
        {showPublicOption && (
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>位置公开设置</Label>
              <div className="text-xs text-gray-500">
                开启后其他用户可以看到您的大概位置
              </div>
            </div>
            <div className="flex items-center space-x-2">
              {location.isLocationPublic ? (
                <Eye className="h-4 w-4 text-green-600" />
              ) : (
                <EyeOff className="h-4 w-4 text-gray-400" />
              )}
              <Switch
                checked={location.isLocationPublic || false}
                onCheckedChange={(checked) => updateLocationSetting('isLocationPublic', checked)}
              />
            </div>
          </div>
        )}

        {/* 本地交易偏好 */}
        {showLocalTradeOption && (
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>偏好本地交易</Label>
              <div className="text-xs text-gray-500">
                优先推荐给附近的用户
              </div>
            </div>
            <Switch
              checked={location.preferLocalTrade || false}
              onCheckedChange={(checked) => updateLocationSetting('preferLocalTrade', checked)}
            />
          </div>
        )}

        {/* 位置标签 */}
        <div className="flex flex-wrap gap-2">
          {location.city && (
            <Badge variant="secondary">
              📍 {location.city}
            </Badge>
          )}
          {location.district && (
            <Badge variant="outline">
              {location.district}
            </Badge>
          )}
          {location.locationRadius && (
            <Badge variant="outline">
              📏 {location.locationRadius}km范围
            </Badge>
          )}
          {location.isLocationPublic && (
            <Badge variant="outline" className="text-green-600">
              👁️ 位置公开
            </Badge>
          )}
          {location.preferLocalTrade && (
            <Badge variant="outline" className="text-blue-600">
              🏠 偏好本地
            </Badge>
          )}
        </div>

        {/* 隐私提示 */}
        <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
          <div className="text-xs text-blue-800">
            <div className="font-medium mb-1">🔒 隐私保护</div>
            <div>• 位置信息仅用于附近搜索和推荐</div>
            <div>• 不会显示您的精确地址</div>
            <div>• 您可以随时修改位置设置</div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
