{"timestamp": "2025-07-17T13:07:43.150Z", "status": "unhealthy", "checks": {"database": {"status": "error", "message": "Cannot find module '../lib/prisma-optimized'\nRequire stack:\n- E:\\Project\\btm\\scripts\\health-check.js"}, "redis": {"status": "not_configured", "message": "Redis未配置或连接失败"}, "api__api_products": {"status": "error", "message": "Request timeout"}, "api__api_health": {"status": "error", "message": "Request timeout"}}, "performance": {"memory": {"rss": 43, "heapTotal": 6, "heapUsed": 5, "external": 2}, "cpu": {"user": 31, "system": 15}, "uptime": 0.0475128}, "recommendations": []}