import { describe, it, expect, beforeEach, vi } from 'vitest'
import { POST as createOrderHandler } from '@/app/api/orders/route'
import { GET as getOrderHandler, PUT as updateOrderHandler } from '@/app/api/orders/[id]/route'
import { POST as paymentHandler, GET as getPaymentHandler } from '@/app/api/orders/[id]/payment/route'
import {
  createMockRequest,
  createMockSession,
  mockUsers,
  mockProducts,
  mockOrders
} from '@/test/test-utils'
import { getServerSession } from 'next-auth/next'

// Mock next-auth
vi.mock('next-auth/next')
const mockGetServerSession = vi.mocked(getServerSession)

// Mock qrcode
vi.mock('qrcode', () => ({
  default: {
    toDataURL: vi.fn().mockResolvedValue('data:image/png;base64,mock-qr-code')
  }
}))

// Mock Prisma
vi.mock('@/lib/prisma', () => ({
  prisma: {
    order: {
      findMany: vi.fn(),
      findUnique: vi.fn(),
      findFirst: vi.fn(),
      create: vi.fn(),
      update: vi.fn()
    },
    product: {
      findUnique: vi.fn(),
      update: vi.fn()
    },
    user: {
      findUnique: vi.fn(),
      update: vi.fn()
    },
    escrowPayment: {
      create: vi.fn(),
      findUnique: vi.fn(),
      findMany: vi.fn(),
      update: vi.fn()
    },
    $transaction: vi.fn()
  }
}))

import { prisma } from '@/lib/prisma'
const mockPrisma = vi.mocked(prisma)

describe('订单API测试', () => {
  beforeEach(() => {
    vi.clearAllMocks()

    // 设置$transaction Mock实现
    mockPrisma.$transaction.mockImplementation(async (callback) => {
      if (typeof callback === 'function') {
        // 创建完整的事务Mock
        const txMock = {
          order: {
            create: vi.fn().mockResolvedValue({ id: 'new-order-id' }),
            findUnique: vi.fn(),
            update: vi.fn()
          },
          orderItem: {
            create: vi.fn().mockResolvedValue({ id: 'new-order-item-id' })
          },
          product: {
            findUnique: vi.fn(),
            update: vi.fn()
          },
          productVariant: {
            findUnique: vi.fn(),
            update: vi.fn()
          }
        }
        return await callback(txMock)
      }
      return []
    })
  })

  describe('POST /api/orders - 创建订单', () => {
    it('应该成功创建订单', async () => {
      const session = createMockSession(mockUsers.buyer)
      mockGetServerSession.mockResolvedValue(session)

      const orderData = {
        productId: mockProducts.available.id,
        quantity: 2,
        shippingAddress: {
          name: '张三',
          phone: '13800138000',
          province: '北京市',
          city: '北京市',
          district: '朝阳区',
          detail: '某某街道123号'
        }
      }

      const productWithSeller = {
        ...mockProducts.available,
        seller: mockUsers.seller
      }

      const createdOrder = {
        ...mockOrders.pending,
        id: 'new-order-id',
        totalAmount: mockProducts.available.price * orderData.quantity,
        productPrice: mockProducts.available.price,
        quantity: orderData.quantity
      }

      mockPrisma.user.findUnique.mockResolvedValue(mockUsers.buyer)
      mockPrisma.product.findUnique.mockResolvedValue(productWithSeller)

      // 更新事务Mock以返回完整的订单数据
      mockPrisma.$transaction.mockImplementation(async (callback) => {
        if (typeof callback === 'function') {
          const txMock = {
            order: {
              create: vi.fn().mockResolvedValue({ id: 'new-order-id' }),
              findUnique: vi.fn().mockResolvedValue(createdOrder)
            },
            orderItem: {
              create: vi.fn().mockResolvedValue({ id: 'new-order-item-id' })
            },
            product: {
              findUnique: vi.fn().mockResolvedValue(productWithSeller),
              update: vi.fn()
            },
            productVariant: {
              findUnique: vi.fn(),
              update: vi.fn()
            }
          }
          return await callback(txMock)
        }
        return createdOrder
      })

      const request = createMockRequest('POST', '/api/orders', orderData)
      const response = await createOrderHandler(request)

      expect(response.status).toBe(201)
      
      const responseData = await response.json()
      expect(responseData).toHaveProperty('id', 'new-order-id')
      expect(responseData).toHaveProperty('status', 'PENDING_PAYMENT')
      expect(responseData).toHaveProperty('totalAmount', mockProducts.available.price * orderData.quantity)

      // 验证事务被调用
      expect(mockPrisma.$transaction).toHaveBeenCalled()
    })

    it('应该拒绝购买自己的商品', async () => {
      const session = createMockSession(mockUsers.seller)
      mockGetServerSession.mockResolvedValue(session)

      const orderData = {
        productId: mockProducts.available.id,
        quantity: 1,
        shippingAddress: {
          name: '张三',
          phone: '13800138000',
          province: '北京市',
          city: '北京市',
          district: '朝阳区',
          detail: '某某街道123号'
        }
      }

      const productWithSeller = {
        ...mockProducts.available,
        sellerId: session.user.id // 同一个用户
      }

      mockPrisma.user.findUnique.mockResolvedValue(mockUsers.seller)
      mockPrisma.product.findUnique.mockResolvedValue(productWithSeller)

      const request = createMockRequest('POST', '/api/orders', orderData)
      const response = await createOrderHandler(request)

      expect(response.status).toBe(400)
      
      const responseData = await response.json()
      expect(responseData).toHaveProperty('error', '不能购买自己的商品')
    })

    it('应该检查库存不足', async () => {
      const session = createMockSession(mockUsers.buyer)
      mockGetServerSession.mockResolvedValue(session)

      const orderData = {
        productId: mockProducts.available.id,
        quantity: 100, // 超过库存
        shippingAddress: {
          name: '张三',
          phone: '13800138000',
          province: '北京市',
          city: '北京市',
          district: '朝阳区',
          detail: '某某街道123号'
        }
      }

      const productWithSeller = {
        ...mockProducts.available,
        stock: 5,
        seller: mockUsers.seller
      }

      mockPrisma.user.findUnique.mockResolvedValue(mockUsers.buyer)
      mockPrisma.product.findUnique.mockResolvedValue(productWithSeller)

      const request = createMockRequest('POST', '/api/orders', orderData)
      const response = await createOrderHandler(request)

      expect(response.status).toBe(400)
      
      const responseData = await response.json()
      expect(responseData).toHaveProperty('error', '库存不足')
    })
  })

  describe('GET /api/orders/[id] - 获取订单详情', () => {
    it('应该返回买家的订单详情', async () => {
      const session = createMockSession(mockUsers.buyer)
      mockGetServerSession.mockResolvedValue(session)

      const orderWithDetails = {
        ...mockOrders.pending,
        product: mockProducts.available,
        buyer: mockUsers.buyer,
        seller: mockUsers.seller,
        messages: []
      }

      mockPrisma.user.findUnique.mockResolvedValue(mockUsers.buyer)
      mockPrisma.order.findUnique.mockResolvedValue(orderWithDetails)

      const request = createMockRequest('GET', `/api/orders/${mockOrders.pending.id}`)
      const response = await getOrderHandler(request, { 
        params: Promise.resolve({ id: mockOrders.pending.id }) 
      })

      expect(response.status).toBe(200)
      
      const responseData = await response.json()
      expect(responseData).toHaveProperty('id', mockOrders.pending.id)
      expect(responseData).toHaveProperty('product')
      expect(responseData).toHaveProperty('buyer')
      expect(responseData).toHaveProperty('seller')
    })

    it('应该拒绝无关用户访问订单', async () => {
      const session = createMockSession(mockUsers.admin)
      mockGetServerSession.mockResolvedValue(session)

      const orderWithDetails = {
        ...mockOrders.pending,
        buyerId: mockUsers.buyer.id,
        sellerId: mockUsers.seller.id
      }

      mockPrisma.user.findUnique.mockResolvedValue(mockUsers.admin)
      mockPrisma.order.findUnique.mockResolvedValue(orderWithDetails)

      const request = createMockRequest('GET', `/api/orders/${mockOrders.pending.id}`)
      const response = await getOrderHandler(request, { 
        params: Promise.resolve({ id: mockOrders.pending.id }) 
      })

      expect(response.status).toBe(403)
      
      const responseData = await response.json()
      expect(responseData).toHaveProperty('error', '无权限查看此订单')
    })
  })

  describe('PATCH /api/orders/[id] - 更新订单状态', () => {
    it('应该允许买家上传支付凭证', async () => {
      const session = createMockSession(mockUsers.buyer)
      mockGetServerSession.mockResolvedValue(session)

      const updateData = {
        action: 'upload_payment',
        paymentScreenshot: '/uploads/payment/screenshot.jpg',
        paymentTxHash: '0xabcdef123456'
      }

      const orderToUpdate = {
        ...mockOrders.pending,
        buyerId: session.user.id
      }

      const updatedOrder = {
        ...orderToUpdate,
        status: 'PAID',
        paymentScreenshot: updateData.paymentScreenshot,
        paymentTxHash: updateData.paymentTxHash,
        paymentConfirmed: false
      }

      mockPrisma.user.findUnique.mockResolvedValue(mockUsers.buyer)
      mockPrisma.order.findUnique.mockResolvedValue(orderToUpdate)
      mockPrisma.order.update.mockResolvedValue(updatedOrder)

      const request = createMockRequest('PATCH', `/api/orders/${orderToUpdate.id}`, updateData)
      const response = await updateOrderHandler(request, { 
        params: Promise.resolve({ id: orderToUpdate.id }) 
      })

      expect(response.status).toBe(200)
      
      const responseData = await response.json()
      expect(responseData).toHaveProperty('status', 'PAID')
      expect(responseData).toHaveProperty('paymentScreenshot', updateData.paymentScreenshot)
    })

    it('应该允许卖家确认支付', async () => {
      const session = createMockSession(mockUsers.seller)
      mockGetServerSession.mockResolvedValue(session)

      const updateData = {
        action: 'confirm_payment'
      }

      const orderToUpdate = {
        ...mockOrders.paid,
        sellerId: session.user.id,
        status: 'PAID',
        paymentConfirmed: false
      }

      const updatedOrder = {
        ...orderToUpdate,
        paymentConfirmed: true
      }

      mockPrisma.user.findUnique.mockResolvedValue(mockUsers.seller)
      mockPrisma.order.findUnique.mockResolvedValue(orderToUpdate)
      mockPrisma.order.update.mockResolvedValue(updatedOrder)

      const request = createMockRequest('PATCH', `/api/orders/${orderToUpdate.id}`, updateData)
      const response = await updateOrderHandler(request, { 
        params: Promise.resolve({ id: orderToUpdate.id }) 
      })

      expect(response.status).toBe(200)
      
      const responseData = await response.json()
      expect(responseData).toHaveProperty('paymentConfirmed', true)
    })
  })

  describe('POST /api/orders/[id]/payment - 处理支付', () => {
    it('应该处理币安支付', async () => {
      const session = createMockSession(mockUsers.buyer)
      mockGetServerSession.mockResolvedValue(session)

      const paymentData = {
        paymentMethod: 'binance_pay',
        orderNumber: 'BN123456789',
        screenshot: '/uploads/payment/binance-screenshot.jpg'
      }

      const orderToPay = {
        ...mockOrders.pending,
        buyerId: session.user.id,
        status: 'PENDING_PAYMENT'
      }

      const updatedOrder = {
        ...orderToPay,
        status: 'PAID',
        paymentMethod: 'binance_pay',
        paymentTxHash: paymentData.orderNumber,
        paymentScreenshot: paymentData.screenshot,
        paymentConfirmed: false
      }

      mockPrisma.order.findUnique.mockResolvedValue(orderToPay)
      mockPrisma.user.findUnique.mockResolvedValue(mockUsers.buyer)
      mockPrisma.order.update.mockResolvedValue(updatedOrder)

      const request = createMockRequest('POST', `/api/orders/${orderToPay.id}/payment`, paymentData)
      const response = await paymentHandler(request, { 
        params: Promise.resolve({ id: orderToPay.id }) 
      })

      expect(response.status).toBe(200)
      
      const responseData = await response.json()
      expect(responseData).toHaveProperty('status', 'PAID')
      expect(responseData).toHaveProperty('paymentMethod', 'binance_pay')
    })

    it('应该处理BNB链支付', async () => {
      const session = createMockSession(mockUsers.buyer)
      mockGetServerSession.mockResolvedValue(session)

      const paymentData = {
        paymentMethod: 'bnb_chain',
        txHash: '******************************************'
      }

      const orderToPay = {
        ...mockOrders.pending,
        buyerId: session.user.id,
        status: 'PENDING_PAYMENT'
      }

      const updatedOrder = {
        ...orderToPay,
        status: 'PAID',
        paymentMethod: 'bnb_chain',
        paymentTxHash: paymentData.txHash,
        paymentConfirmed: false
      }

      mockPrisma.order.findUnique.mockResolvedValue(orderToPay)
      mockPrisma.user.findUnique.mockResolvedValue(mockUsers.buyer)
      mockPrisma.order.update.mockResolvedValue(updatedOrder)

      const request = createMockRequest('POST', `/api/orders/${orderToPay.id}/payment`, paymentData)
      const response = await paymentHandler(request, { 
        params: Promise.resolve({ id: orderToPay.id }) 
      })

      expect(response.status).toBe(200)
      
      const responseData = await response.json()
      expect(responseData).toHaveProperty('status', 'PAID')
      expect(responseData).toHaveProperty('paymentMethod', 'bnb_chain')
    })
  })

  describe('GET /api/orders/[id]/payment - 获取支付信息', () => {
    it('应该为买家生成支付二维码', async () => {
      const session = createMockSession(mockUsers.buyer)
      mockGetServerSession.mockResolvedValue(session)

      const orderForPayment = {
        ...mockOrders.pending,
        buyerId: session.user.id,
        status: 'PENDING_PAYMENT'
      }

      mockPrisma.order.findUnique.mockResolvedValue(orderForPayment)
      mockPrisma.user.findUnique.mockResolvedValue(mockUsers.buyer)
      mockPrisma.escrowPayment.findMany.mockResolvedValue([])

      const request = createMockRequest('GET', `/api/orders/${orderForPayment.id}/payment`)
      const response = await getPaymentHandler(request, { 
        params: Promise.resolve({ id: orderForPayment.id }) 
      })

      expect(response.status).toBe(200)
      
      const responseData = await response.json()
      expect(responseData).toHaveProperty('order')
      expect(responseData).toHaveProperty('paymentQRCode')
      expect(responseData.paymentQRCode).toBe('data:image/png;base64,mock-qr-code')
    })
  })
})
