import { vi, expect } from 'vitest'
import { NextRequest, NextResponse } from 'next/server'
import type { Session } from 'next-auth'

// Mock Prisma - 与setup.ts保持一致
export const mockPrisma = {
  user: {
    findUnique: vi.fn(),
    findMany: vi.fn(),
    findFirst: vi.fn(),
    create: vi.fn(),
    update: vi.fn(),
    upsert: vi.fn(),
    delete: vi.fn(),
    deleteMany: vi.fn(),
    count: vi.fn(),
    aggregate: vi.fn(),
    groupBy: vi.fn()
  },
  product: {
    findUnique: vi.fn(),
    findMany: vi.fn(),
    findFirst: vi.fn(),
    create: vi.fn(),
    update: vi.fn(),
    upsert: vi.fn(),
    delete: vi.fn(),
    deleteMany: vi.fn(),
    count: vi.fn(),
    aggregate: vi.fn(),
    groupBy: vi.fn()
  },
  order: {
    findUnique: vi.fn(),
    findMany: vi.fn(),
    findFirst: vi.fn(),
    create: vi.fn(),
    update: vi.fn(),
    upsert: vi.fn(),
    delete: vi.fn(),
    deleteMany: vi.fn(),
    count: vi.fn(),
    aggregate: vi.fn(),
    groupBy: vi.fn()
  },
  message: {
    findUnique: vi.fn(),
    findMany: vi.fn(),
    findFirst: vi.fn(),
    create: vi.fn(),
    update: vi.fn(),
    upsert: vi.fn(),
    delete: vi.fn(),
    deleteMany: vi.fn(),
    count: vi.fn()
  },
  announcement: {
    findUnique: vi.fn(),
    findMany: vi.fn(),
    findFirst: vi.fn(),
    create: vi.fn(),
    update: vi.fn(),
    delete: vi.fn(),
    count: vi.fn()
  },
  review: {
    findUnique: vi.fn(),
    findMany: vi.fn(),
    findFirst: vi.fn(),
    create: vi.fn(),
    update: vi.fn(),
    delete: vi.fn(),
    count: vi.fn()
  },
  escrowPayment: {
    findUnique: vi.fn(),
    findMany: vi.fn(),
    findFirst: vi.fn(),
    create: vi.fn(),
    update: vi.fn(),
    delete: vi.fn(),
    count: vi.fn()
  },
  userSession: {
    findUnique: vi.fn(),
    findMany: vi.fn(),
    create: vi.fn(),
    update: vi.fn(),
    delete: vi.fn(),
    deleteMany: vi.fn()
  },
  securityLog: {
    findMany: vi.fn(),
    create: vi.fn(),
    count: vi.fn()
  },
  $transaction: vi.fn(),
  $disconnect: vi.fn(),
  $connect: vi.fn(),
  $executeRaw: vi.fn(),
  $queryRaw: vi.fn()
}

// 测试用户数据
export const mockUsers = {
  buyer: {
    id: 'buyer-id-123',
    userId: 'user-buyer123',
    email: '<EMAIL>',
    name: 'Test Buyer',
    role: 'USER',
    creditScore: 85,
    depositBalance: 100.0,
    status: 'ACTIVE',
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01')
  },
  seller: {
    id: 'seller-id-123',
    userId: 'user-seller123',
    email: '<EMAIL>',
    name: 'Test Seller',
    role: 'USER',
    creditScore: 92,
    depositBalance: 200.0,
    status: 'ACTIVE',
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01')
  },
  admin: {
    id: 'admin-id-123',
    userId: 'user-admin123',
    email: '<EMAIL>',
    name: 'Test Admin',
    role: 'ADMIN',
    creditScore: 100,
    depositBalance: 0,
    status: 'ACTIVE',
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01')
  }
}

// 测试商品数据
export const mockProducts = {
  available: {
    id: 'product-id-123',
    title: 'Test Product',
    description: 'A test product for testing',
    price: 50.0,
    stock: 10,
    status: 'AVAILABLE',
    reviewStatus: 'APPROVED',
    category: 'ELECTRONICS',
    condition: 'NEW',
    sellerId: mockUsers.seller.id,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01')
  },
  pending: {
    id: 'product-id-456',
    title: 'Pending Product',
    description: 'A product pending review',
    price: 75.0,
    stock: 5,
    status: 'AVAILABLE',
    reviewStatus: 'PENDING',
    category: 'CLOTHING',
    condition: 'USED_LIKE_NEW',
    sellerId: mockUsers.seller.id,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01')
  }
}

// 测试订单数据
export const mockOrders = {
  pending: {
    id: 'order-id-123',
    orderNumber: 'ORD-123456',
    status: 'PENDING_PAYMENT',
    totalAmount: 50.0,
    productPrice: 50.0,
    shippingFee: 0,
    platformFee: 0,
    paymentMethod: null,
    paymentConfirmed: false,
    escrowStatus: 'PENDING',
    productId: mockProducts.available.id,
    buyerId: mockUsers.buyer.id,
    sellerId: mockUsers.seller.id,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01')
  },
  paid: {
    id: 'order-id-456',
    orderNumber: 'ORD-456789',
    status: 'PAID',
    totalAmount: 75.0,
    productPrice: 75.0,
    shippingFee: 0,
    platformFee: 0,
    paymentMethod: 'BINANCE_PAY',
    paymentConfirmed: true,
    escrowStatus: 'FUNDED',
    productId: mockProducts.pending.id,
    buyerId: mockUsers.buyer.id,
    sellerId: mockUsers.seller.id,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01')
  }
}

// 测试消息数据
export const mockMessages = {
  text: {
    id: 'message-id-123',
    content: 'Hello, is this item still available?',
    messageType: 'TEXT',
    status: 'SENT',
    orderId: mockOrders.pending.id,
    senderId: mockUsers.buyer.id,
    receiverId: mockUsers.seller.id,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01')
  },
  image: {
    id: 'message-id-456',
    content: '',
    messageType: 'IMAGE',
    status: 'SENT',
    fileUrl: '/uploads/chat/test-image.jpg',
    fileName: 'test-image.jpg',
    fileSize: 1024,
    fileMimeType: 'image/jpeg',
    orderId: mockOrders.pending.id,
    senderId: mockUsers.seller.id,
    receiverId: mockUsers.buyer.id,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01')
  }
}

// 创建模拟会话
export const createMockSession = (user: typeof mockUsers.buyer): Session => ({
  user: {
    id: user.id,
    email: user.email,
    name: user.name,
    userId: user.userId,
    role: user.role
  },
  expires: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
})

// 创建模拟请求
export const createMockRequest = (
  method: string = 'GET',
  url: string = 'http://localhost:3000/api/test',
  body?: any,
  headers?: Record<string, string>
): NextRequest => {
  // 确保URL是绝对URL
  const absoluteUrl = url.startsWith('http') ? url : `http://localhost:3000${url}`

  const request = new NextRequest(absoluteUrl, {
    method,
    headers: {
      'Content-Type': 'application/json',
      ...headers
    },
    body: body ? JSON.stringify(body) : undefined
  })

  return request
}

// 创建模拟响应检查器
export const expectJsonResponse = async (
  response: NextResponse,
  expectedStatus: number,
  expectedData?: any
) => {
  expect(response.status).toBe(expectedStatus)
  
  if (expectedData) {
    const data = await response.json()
    expect(data).toEqual(expect.objectContaining(expectedData))
  }
}

// 数据库清理工具
export const cleanupDatabase = async () => {
  // 在实际测试中，这里会清理测试数据库
  // 目前使用mock，所以只需要重置mock
  vi.clearAllMocks()
}

// 设置Prisma Mock的默认行为
export const setupPrismaMocks = () => {
  // 用户相关Mock
  mockPrisma.user.findUnique.mockImplementation(async ({ where }) => {
    if (where.id === mockUsers.buyer.userId) return mockUsers.buyer
    if (where.id === mockUsers.seller.userId) return mockUsers.seller
    if (where.id === mockUsers.admin.userId) return mockUsers.admin
    if (where.email === mockUsers.buyer.email) return mockUsers.buyer
    if (where.email === mockUsers.seller.email) return mockUsers.seller
    return null
  })

  mockPrisma.user.findMany.mockResolvedValue([mockUsers.buyer, mockUsers.seller])
  mockPrisma.user.count.mockResolvedValue(2)

  // 商品相关Mock
  mockPrisma.product.findUnique.mockImplementation(async ({ where }) => {
    if (where.id === mockProducts.available.id) return mockProducts.available
    if (where.id === mockProducts.pending.id) return mockProducts.pending
    return null
  })

  mockPrisma.product.findMany.mockResolvedValue([mockProducts.available, mockProducts.pending])
  mockPrisma.product.count.mockResolvedValue(2)

  // 订单相关Mock
  mockPrisma.order.findUnique.mockImplementation(async ({ where }) => {
    if (where.id === mockOrders.pending.id) return mockOrders.pending
    if (where.id === mockOrders.paid.id) return mockOrders.paid
    return null
  })

  mockPrisma.order.findMany.mockResolvedValue([mockOrders.pending, mockOrders.paid])
  mockPrisma.order.count.mockResolvedValue(2)

  // 消息相关Mock
  mockPrisma.message.findMany.mockResolvedValue([mockMessages.text, mockMessages.image])
  mockPrisma.message.count.mockResolvedValue(2)

  // 事务Mock
  mockPrisma.$transaction.mockImplementation(async (callback) => {
    if (typeof callback === 'function') {
      return await callback(mockPrisma)
    }
    return []
  })
}

// 等待异步操作完成
export const waitFor = (ms: number) => new Promise(resolve => setTimeout(resolve, ms))

// 生成随机测试数据
export const generateRandomString = (length: number = 10): string => {
  return Math.random().toString(36).substring(2, 2 + length)
}

export const generateRandomEmail = (): string => {
  return `test-${generateRandomString()}@example.com`
}

export const generateRandomUserId = (): string => {
  return `user-${generateRandomString(12)}`
}

// 文件上传模拟
export const createMockFile = (
  name: string = 'test.jpg',
  type: string = 'image/jpeg',
  size: number = 1024
): File => {
  const file = new File(['test content'], name, { type })
  Object.defineProperty(file, 'size', { value: size })
  return file
}

// 表单数据创建工具
export const createMockFormData = (data: Record<string, any>): FormData => {
  const formData = new FormData()
  Object.entries(data).forEach(([key, value]) => {
    if (value instanceof File) {
      formData.append(key, value)
    } else {
      formData.append(key, String(value))
    }
  })
  return formData
}

// 数据库测试工具
export class DatabaseTestHelper {
  static async seedTestData() {
    // 在实际测试中，这里会向测试数据库插入数据
    // 目前使用mock，所以只需要设置mock返回值
    mockPrisma.user.findMany.mockResolvedValue([mockUsers.buyer, mockUsers.seller, mockUsers.admin])
    mockPrisma.product.findMany.mockResolvedValue([mockProducts.available, mockProducts.pending])
    mockPrisma.order.findMany.mockResolvedValue([mockOrders.pending, mockOrders.paid])
  }

  static async clearTestData() {
    // 清理测试数据
    vi.clearAllMocks()
  }

  static async createTestUser(userData: Partial<any> = {}) {
    const user = { ...mockUsers.buyer, ...userData }
    mockPrisma.user.create.mockResolvedValue(user)
    mockPrisma.user.findUnique.mockResolvedValue(user)
    return user
  }

  static async createTestProduct(productData: Partial<any> = {}) {
    const product = { ...mockProducts.available, ...productData }
    mockPrisma.product.create.mockResolvedValue(product)
    mockPrisma.product.findUnique.mockResolvedValue(product)
    return product
  }

  static async createTestOrder(orderData: Partial<any> = {}) {
    const order = { ...mockOrders.pending, ...orderData }
    mockPrisma.order.create.mockResolvedValue(order)
    mockPrisma.order.findUnique.mockResolvedValue(order)
    return order
  }
}

// API测试工具
export class ApiTestHelper {
  static async makeAuthenticatedRequest(
    method: string,
    url: string,
    user: any,
    body?: any
  ) {
    const session = createMockSession(user)

    // Mock session
    const { getServerSession } = await import('next-auth/next')
    vi.mocked(getServerSession).mockResolvedValue(session)

    return createMockRequest(method, url, body, {
      'Authorization': `Bearer ${user.id}`
    })
  }

  static async expectApiError(
    response: Response,
    expectedStatus: number,
    expectedMessage?: string
  ) {
    expect(response.status).toBe(expectedStatus)

    if (expectedMessage) {
      const data = await response.json()
      expect(data.error).toContain(expectedMessage)
    }
  }

  static async expectApiSuccess(
    response: Response,
    expectedData?: any
  ) {
    expect(response.status).toBeLessThan(400)

    if (expectedData) {
      const data = await response.json()
      expect(data).toEqual(expect.objectContaining(expectedData))
    }
  }
}

// 时间测试工具
export class TimeTestHelper {
  static mockCurrentTime(date: Date) {
    vi.useFakeTimers()
    vi.setSystemTime(date)
  }

  static restoreTime() {
    vi.useRealTimers()
  }

  static async advanceTime(ms: number) {
    vi.advanceTimersByTime(ms)
    await vi.runAllTimersAsync()
  }

  static createExpiredDate(daysAgo: number = 1) {
    return new Date(Date.now() - daysAgo * 24 * 60 * 60 * 1000)
  }

  static createFutureDate(daysFromNow: number = 1) {
    return new Date(Date.now() + daysFromNow * 24 * 60 * 60 * 1000)
  }
}

// 文件测试工具
export class FileTestHelper {
  static createMockImageFile(
    name: string = 'test.jpg',
    size: number = 1024,
    type: string = 'image/jpeg'
  ): File {
    const content = new Array(size).fill('a').join('')
    return new File([content], name, { type })
  }

  static createMockVideoFile(
    name: string = 'test.mp4',
    size: number = 1024 * 1024,
    type: string = 'video/mp4'
  ): File {
    const content = new Array(size).fill('v').join('')
    return new File([content], name, { type })
  }

  static createMockDocumentFile(
    name: string = 'test.pdf',
    size: number = 1024,
    type: string = 'application/pdf'
  ): File {
    const content = new Array(size).fill('d').join('')
    return new File([content], name, { type })
  }

  static mockFileUploadResponse(url: string, size: number) {
    return {
      success: true,
      url,
      fileName: url.split('/').pop(),
      size,
      originalSize: size * 1.2
    }
  }
}

// WebSocket测试工具
export class WebSocketTestHelper {
  static createMockSocket() {
    return {
      connected: true,
      emit: vi.fn(),
      on: vi.fn(),
      off: vi.fn(),
      once: vi.fn(),
      connect: vi.fn(),
      disconnect: vi.fn(),
      addEventListener: vi.fn(),
      removeEventListener: vi.fn()
    }
  }

  static mockSocketConnection(events: Record<string, any> = {}) {
    const socket = this.createMockSocket()

    // 模拟事件监听
    socket.on.mockImplementation((event: string, callback: Function) => {
      if (events[event]) {
        setTimeout(() => callback(events[event]), 10)
      }
    })

    return socket
  }

  static createMockSocketMessage(type: string, data: any) {
    return {
      type,
      data,
      timestamp: new Date().toISOString(),
      id: generateRandomString()
    }
  }
}

// 性能测试工具
export class PerformanceTestHelper {
  static async measureExecutionTime<T>(fn: () => Promise<T>): Promise<{ result: T; time: number }> {
    const start = performance.now()
    const result = await fn()
    const end = performance.now()
    return { result, time: end - start }
  }

  static async measureMemoryUsage<T>(fn: () => Promise<T>): Promise<{ result: T; memoryDelta: number }> {
    const initialMemory = process.memoryUsage().heapUsed
    const result = await fn()
    const finalMemory = process.memoryUsage().heapUsed
    return { result, memoryDelta: finalMemory - initialMemory }
  }

  static createLoadTest(
    requestFn: () => Promise<any>,
    options: {
      concurrency: number
      duration: number
      rampUp?: number
    }
  ) {
    return async () => {
      const { concurrency, duration, rampUp = 0 } = options
      const results: any[] = []
      const errors: any[] = []
      const startTime = performance.now()
      const endTime = startTime + duration

      // 渐进式增加并发
      const rampUpInterval = rampUp / concurrency
      let currentConcurrency = 1

      while (performance.now() < endTime) {
        const requests = Array.from({ length: currentConcurrency }, async () => {
          try {
            const result = await requestFn()
            results.push(result)
          } catch (error) {
            errors.push(error)
          }
        })

        await Promise.allSettled(requests)

        if (rampUp > 0 && currentConcurrency < concurrency) {
          currentConcurrency = Math.min(concurrency, currentConcurrency + 1)
          await waitFor(rampUpInterval)
        }
      }

      return {
        totalRequests: results.length + errors.length,
        successfulRequests: results.length,
        failedRequests: errors.length,
        successRate: (results.length / (results.length + errors.length)) * 100,
        duration: performance.now() - startTime
      }
    }
  }
}
