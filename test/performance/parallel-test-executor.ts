import { Worker, isMainThread, parentPort, workerData } from 'worker_threads'
import { cpus } from 'os'
import { performance } from 'perf_hooks'

export interface TestTask {
  id: string
  name: string
  type: 'data-generation' | 'api-simulation' | 'database-query' | 'cache-operation'
  params: any
  priority: number
}

export interface TestResult {
  taskId: string
  success: boolean
  duration: number
  result?: any
  error?: string
  memoryUsage: number
}

export class ParallelTestExecutor {
  private workers: Worker[] = []
  private taskQueue: TestTask[] = []
  private results: Map<string, TestResult> = new Map()
  private activeWorkers = 0
  private maxWorkers: number

  constructor(maxWorkers?: number) {
    this.maxWorkers = maxWorkers || Math.min(cpus().length, 8)
  }

  // 添加测试任务
  addTask(task: TestTask): void {
    this.taskQueue.push(task)
  }

  // 批量添加任务
  addTasks(tasks: TestTask[]): void {
    this.taskQueue.push(...tasks)
  }

  // 执行所有任务
  async executeAll(): Promise<Map<string, TestResult>> {
    console.log(`🚀 开始并行执行 ${this.taskQueue.length} 个任务，使用 ${this.maxWorkers} 个工作线程`)
    
    const startTime = performance.now()
    
    // 按优先级排序任务
    this.taskQueue.sort((a, b) => b.priority - a.priority)
    
    // 创建工作线程池
    await this.createWorkerPool()
    
    // 分发任务
    await this.distributeTasks()
    
    // 等待所有任务完成
    await this.waitForCompletion()
    
    // 清理工作线程
    await this.cleanup()
    
    const endTime = performance.now()
    const totalDuration = endTime - startTime
    
    console.log(`✅ 所有任务执行完成，总耗时: ${totalDuration.toFixed(2)}ms`)
    console.log(`📊 成功: ${Array.from(this.results.values()).filter(r => r.success).length}`)
    console.log(`❌ 失败: ${Array.from(this.results.values()).filter(r => !r.success).length}`)
    
    return this.results
  }

  // 创建工作线程池
  private async createWorkerPool(): Promise<void> {
    for (let i = 0; i < this.maxWorkers; i++) {
      const worker = new Worker(__filename, {
        workerData: { workerId: i }
      })
      
      worker.on('message', (result: TestResult) => {
        this.results.set(result.taskId, result)
        this.activeWorkers--
        
        // 分发下一个任务
        this.assignNextTask(worker)
      })
      
      worker.on('error', (error) => {
        console.error(`Worker ${i} error:`, error)
        this.activeWorkers--
      })
      
      this.workers.push(worker)
    }
  }

  // 分发任务
  private async distributeTasks(): Promise<void> {
    for (const worker of this.workers) {
      this.assignNextTask(worker)
    }
  }

  // 分配下一个任务给工作线程
  private assignNextTask(worker: Worker): void {
    if (this.taskQueue.length === 0) return
    
    const task = this.taskQueue.shift()!
    this.activeWorkers++
    
    worker.postMessage(task)
  }

  // 等待所有任务完成
  private async waitForCompletion(): Promise<void> {
    return new Promise((resolve) => {
      const checkCompletion = () => {
        if (this.activeWorkers === 0 && this.taskQueue.length === 0) {
          resolve()
        } else {
          setTimeout(checkCompletion, 100)
        }
      }
      checkCompletion()
    })
  }

  // 清理工作线程
  private async cleanup(): Promise<void> {
    await Promise.all(this.workers.map(worker => worker.terminate()))
    this.workers = []
  }

  // 获取执行统计
  getStats() {
    const results = Array.from(this.results.values())
    const successful = results.filter(r => r.success)
    const failed = results.filter(r => !r.success)
    
    return {
      total: results.length,
      successful: successful.length,
      failed: failed.length,
      successRate: (successful.length / results.length) * 100,
      avgDuration: successful.reduce((sum, r) => sum + r.duration, 0) / successful.length,
      totalMemoryUsage: results.reduce((sum, r) => sum + r.memoryUsage, 0)
    }
  }
}

// 工作线程执行逻辑
if (!isMainThread) {
  const { workerId } = workerData
  
  parentPort?.on('message', async (task: TestTask) => {
    const startTime = performance.now()
    const memoryBefore = process.memoryUsage().heapUsed
    
    try {
      const result = await executeTask(task)
      const endTime = performance.now()
      const memoryAfter = process.memoryUsage().heapUsed
      
      const testResult: TestResult = {
        taskId: task.id,
        success: true,
        duration: endTime - startTime,
        result,
        memoryUsage: memoryAfter - memoryBefore
      }
      
      parentPort?.postMessage(testResult)
    } catch (error) {
      const endTime = performance.now()
      const memoryAfter = process.memoryUsage().heapUsed
      
      const testResult: TestResult = {
        taskId: task.id,
        success: false,
        duration: endTime - startTime,
        error: error instanceof Error ? error.message : String(error),
        memoryUsage: memoryAfter - memoryBefore
      }
      
      parentPort?.postMessage(testResult)
    }
  })
  
  // 执行具体任务
  async function executeTask(task: TestTask): Promise<any> {
    switch (task.type) {
      case 'data-generation':
        return await executeDataGeneration(task.params)
      case 'api-simulation':
        return await executeApiSimulation(task.params)
      case 'database-query':
        return await executeDatabaseQuery(task.params)
      case 'cache-operation':
        return await executeCacheOperation(task.params)
      default:
        throw new Error(`Unknown task type: ${task.type}`)
    }
  }
  
  // 数据生成任务
  async function executeDataGeneration(params: any): Promise<any> {
    const { OptimizedUserFactory, OptimizedProductFactory, OptimizedOrderFactory } = require('./optimized-data-factory')
    
    switch (params.dataType) {
      case 'users':
        return OptimizedUserFactory.createBatch(params.count)
      case 'products':
        return OptimizedProductFactory.createBatch(params.count)
      case 'orders':
        return OptimizedOrderFactory.createBatch(params.count)
      default:
        throw new Error(`Unknown data type: ${params.dataType}`)
    }
  }
  
  // API模拟任务
  async function executeApiSimulation(params: any): Promise<any> {
    const { endpoint, method, data, delay } = params
    
    // 模拟网络延迟
    if (delay) {
      await new Promise(resolve => setTimeout(resolve, delay))
    }
    
    // 模拟API响应
    return {
      endpoint,
      method,
      status: 200,
      data: { success: true, timestamp: Date.now() },
      responseTime: Math.random() * 100 + 10
    }
  }
  
  // 数据库查询模拟任务
  async function executeDatabaseQuery(params: any): Promise<any> {
    const { queryType, table, conditions, limit } = params
    
    // 模拟查询延迟
    const queryDelay = Math.random() * 50 + 5
    await new Promise(resolve => setTimeout(resolve, queryDelay))
    
    // 模拟查询结果
    const mockResults = Array.from({ length: limit || 10 }, (_, i) => ({
      id: `${table}-${i}`,
      data: `mock-data-${i}`,
      timestamp: Date.now()
    }))
    
    return {
      queryType,
      table,
      results: mockResults,
      queryTime: queryDelay,
      rowsAffected: mockResults.length
    }
  }
  
  // 缓存操作任务
  async function executeCacheOperation(params: any): Promise<any> {
    const { operation, key, value, ttl } = params
    
    // 模拟缓存操作
    const cache = new Map()
    
    switch (operation) {
      case 'set':
        cache.set(key, { value, expires: Date.now() + (ttl || 3600) * 1000 })
        return { success: true, key, operation: 'set' }
      case 'get':
        const cached = cache.get(key)
        return { 
          success: true, 
          key, 
          operation: 'get',
          hit: !!cached,
          value: cached?.value 
        }
      case 'delete':
        const deleted = cache.delete(key)
        return { success: true, key, operation: 'delete', deleted }
      default:
        throw new Error(`Unknown cache operation: ${operation}`)
    }
  }
}

// 任务生成器工具
export class TaskGenerator {
  static generateDataGenerationTasks(counts: { users: number; products: number; orders: number }): TestTask[] {
    const tasks: TestTask[] = []
    let taskId = 1
    
    // 用户生成任务
    if (counts.users > 0) {
      const batchSize = 1000
      const batches = Math.ceil(counts.users / batchSize)
      
      for (let i = 0; i < batches; i++) {
        const batchCount = Math.min(batchSize, counts.users - i * batchSize)
        tasks.push({
          id: `user-gen-${taskId++}`,
          name: `Generate ${batchCount} users (batch ${i + 1}/${batches})`,
          type: 'data-generation',
          params: { dataType: 'users', count: batchCount },
          priority: 3
        })
      }
    }
    
    // 商品生成任务
    if (counts.products > 0) {
      const batchSize = 1000
      const batches = Math.ceil(counts.products / batchSize)
      
      for (let i = 0; i < batches; i++) {
        const batchCount = Math.min(batchSize, counts.products - i * batchSize)
        tasks.push({
          id: `product-gen-${taskId++}`,
          name: `Generate ${batchCount} products (batch ${i + 1}/${batches})`,
          type: 'data-generation',
          params: { dataType: 'products', count: batchCount },
          priority: 2
        })
      }
    }
    
    // 订单生成任务
    if (counts.orders > 0) {
      const batchSize = 1000
      const batches = Math.ceil(counts.orders / batchSize)
      
      for (let i = 0; i < batches; i++) {
        const batchCount = Math.min(batchSize, counts.orders - i * batchSize)
        tasks.push({
          id: `order-gen-${taskId++}`,
          name: `Generate ${batchCount} orders (batch ${i + 1}/${batches})`,
          type: 'data-generation',
          params: { dataType: 'orders', count: batchCount },
          priority: 1
        })
      }
    }
    
    return tasks
  }
  
  static generateApiSimulationTasks(endpoints: string[], requestsPerEndpoint: number): TestTask[] {
    const tasks: TestTask[] = []
    let taskId = 1
    
    for (const endpoint of endpoints) {
      for (let i = 0; i < requestsPerEndpoint; i++) {
        tasks.push({
          id: `api-sim-${taskId++}`,
          name: `API simulation ${endpoint} #${i + 1}`,
          type: 'api-simulation',
          params: {
            endpoint,
            method: 'GET',
            delay: Math.random() * 50 + 10
          },
          priority: 2
        })
      }
    }
    
    return tasks
  }
}
