{"name": "web", "version": "1.3.2", "private": true, "scripts": {"dev": "node server.js", "dev:optimized": "node scripts/performance-setup.js && node server.js", "dev:next": "next dev --turbopack", "dev:fast": "next dev --turbopack --experimental-https", "build": "next build", "build:analyze": "ANALYZE=true next build", "start": "NODE_ENV=production node scripts/performance-setup.js && node server.js", "start:next": "next start", "start:cluster": "USE_CLUSTER=true NODE_ENV=production node server.js", "lint": "next lint", "lint:fix": "next lint --fix", "test": "vitest", "test:unit": "vitest run test/**/*.test.ts", "test:integration": "node scripts/test-runner.js integration", "test:e2e": "node scripts/test-runner.js e2e", "test:performance": "node scripts/test-runner.js performance", "test:all": "node scripts/test-runner.js all", "test:watch": "vitest --watch", "test:coverage": "vitest run --coverage", "test:setup": "bash scripts/test-setup.sh --setup", "test:cleanup": "bash scripts/test-setup.sh --cleanup", "test:ci": "vitest run --coverage --reporter=json --outputFile=test-results/results.json", "perf:setup": "node scripts/performance-setup.js", "perf:monitor": "node scripts/performance-monitor.js", "cache:clear": "node scripts/clear-cache.js", "checkpoint": "node scripts/history-manager.js create", "history": "node scripts/history-manager.js list", "rollback": "node scripts/history-manager.js rollback", "safe-migrate": "node scripts/history-manager.js auto '数据库迁移' && npx prisma migrate dev", "safe-build": "node scripts/history-manager.js auto '构建前检查点' && npm run build", "db:optimize": "node scripts/optimize-database.js", "health:check": "node scripts/health-check.js", "type:check": "tsc --noEmit", "type:check:watch": "tsc --noEmit --watch", "test:auth-types": "npx tsx scripts/test-auth-types.ts", "verify:auth-fix": "node scripts/verify-auth-fix.js", "kill:port": "node scripts/kill-port.js", "dev:clean": "node scripts/kill-port.js 3000 && npm run dev", "release": "node scripts/release.js", "release:minor": "node scripts/release.js --version-type minor", "release:major": "node scripts/release.js --version-type major", "release:patch": "node scripts/release.js --version-type patch", "release:current": "node scripts/release.js --version-type current", "release:dry": "node scripts/release.js --dry-run", "package:create": "node scripts/create-release-package.js", "docker:setup": "bash scripts/docker-setup.sh", "docker:setup:redis": "bash scripts/docker-setup.sh --redis", "docker:start": "bash scripts/docker-manage.sh start", "docker:start:redis": "bash scripts/docker-manage.sh start --redis", "docker:stop": "bash scripts/docker-manage.sh stop", "docker:restart": "bash scripts/docker-manage.sh restart", "docker:status": "bash scripts/docker-manage.sh status", "docker:logs": "bash scripts/docker-manage.sh logs", "docker:backup": "bash scripts/docker-manage.sh backup", "docker:shell": "bash scripts/docker-manage.sh shell", "docker:migrate": "bash scripts/docker-manage.sh migrate", "docker:reset": "bash scripts/docker-manage.sh reset", "docker:clean": "bash scripts/docker-manage.sh clean", "docker:test": "bash scripts/docker-config-test.sh", "test:accounts": "node scripts/create-test-accounts.js", "test:accounts:reset": "npx prisma migrate reset --force && node scripts/create-test-accounts.js", "test:accounts:verify": "node scripts/verify-test-accounts.js", "test:login": "node scripts/test-login-debug.js", "migrate:products": "node scripts/migrate-product-data.js", "demo:enhanced-features": "node scripts/demo-enhanced-features.js", "clean:test-products": "node scripts/clean-test-products.js", "generate:china-cities": "node scripts/fetch-china-cities.js", "test:optimizations": "node scripts/test-optimizations.js", "test:dropdown-fix": "node scripts/test-dropdown-fix.js", "test:escrow-system": "node scripts/test-escrow-system.js", "demo:escrow": "node scripts/demo-escrow-system.js"}, "prisma": {"schema": "./prisma/schema.prisma"}, "dependencies": {"@heroicons/react": "^2.2.0", "@prisma/client": "^6.11.1", "@socket.io/redis-adapter": "^8.3.0", "@tinymce/tinymce-react": "^6.2.1", "@types/pdfkit": "^0.17.2", "axios": "^1.11.0", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dayjs": "^1.11.13", "dotenv": "^17.2.0", "ethers": "^6.15.0", "form-data": "^4.0.3", "ioredis": "^5.6.1", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.525.0", "minio": "^8.0.5", "multer": "^2.0.1", "mysql2": "^3.14.2", "next": "15.3.5", "next-auth": "^4.24.11", "node-fetch": "^3.3.2", "pdfkit": "^0.17.1", "qrcode": "^1.5.4", "react": "^19.0.0", "react-dom": "^19.0.0", "redis": "^5.6.0", "sharp": "^0.34.3", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "tailwind-merge": "^3.3.1", "tinymce": "^7.9.1", "ws": "^8.18.3", "zod": "^4.0.5", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/eslintrc": "^3", "@faker-js/faker": "^9.4.0", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.5.2", "@types/bcryptjs": "^3.0.0", "@types/jsonwebtoken": "^9.0.10", "@types/multer": "^2.0.0", "@types/node": "^20", "@types/qrcode": "^1.5.5", "@types/react": "^19", "@types/react-dom": "^19", "@types/ws": "^8.18.1", "@vitejs/plugin-react": "^4.6.0", "@vitest/coverage-v8": "^3.2.4", "eslint": "^9", "eslint-config-next": "15.3.5", "jsdom": "^26.1.0", "prisma": "^6.11.1", "tailwindcss": "^4", "typescript": "^5", "vitest": "^3.2.4"}}