// 客户端设备信息获取工具

export interface DeviceInfo {
  deviceName: string
  deviceType: 'desktop' | 'mobile' | 'tablet'
  browser: string
  browserVersion: string
  os: string
  osVersion: string
  platform: string
  language: string
  timezone: string
  screenResolution: string
  location?: string
}

// 获取详细的设备信息
export function getDeviceInfo(): DeviceInfo {
  const userAgent = navigator.userAgent
  const platform = navigator.platform
  const language = navigator.language
  const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone
  const screenResolution = `${screen.width}x${screen.height}`

  // 解析浏览器信息
  const browserInfo = parseBrowser(userAgent)
  
  // 解析操作系统信息
  const osInfo = parseOS(userAgent, platform)
  
  // 解析设备类型
  const deviceType = parseDeviceType(userAgent)

  // 生成设备名称
  const deviceName = generateDeviceName(browserInfo, osInfo, deviceType)

  return {
    deviceName,
    deviceType,
    browser: browserInfo.name,
    browserVersion: browserInfo.version,
    os: osInfo.name,
    osVersion: osInfo.version,
    platform,
    language,
    timezone,
    screenResolution
  }
}

// 解析浏览器信息
function parseBrowser(userAgent: string): { name: string; version: string } {
  const ua = userAgent.toLowerCase()
  
  // Chrome (需要在Safari之前检查，因为Chrome也包含Safari字符串)
  if (ua.includes('chrome') && !ua.includes('edg') && !ua.includes('opr')) {
    const match = ua.match(/chrome\/(\d+\.?\d*\.?\d*\.?\d*)/)
    return {
      name: 'Chrome',
      version: match ? match[1] : 'Unknown'
    }
  }
  
  // Edge
  if (ua.includes('edg')) {
    const match = ua.match(/edg\/(\d+\.?\d*\.?\d*\.?\d*)/)
    return {
      name: 'Edge',
      version: match ? match[1] : 'Unknown'
    }
  }
  
  // Firefox
  if (ua.includes('firefox')) {
    const match = ua.match(/firefox\/(\d+\.?\d*\.?\d*)/)
    return {
      name: 'Firefox',
      version: match ? match[1] : 'Unknown'
    }
  }
  
  // Safari (需要在最后检查)
  if (ua.includes('safari') && !ua.includes('chrome')) {
    const match = ua.match(/version\/(\d+\.?\d*\.?\d*)/)
    return {
      name: 'Safari',
      version: match ? match[1] : 'Unknown'
    }
  }
  
  // Opera
  if (ua.includes('opr') || ua.includes('opera')) {
    const match = ua.match(/(?:opr|opera)\/(\d+\.?\d*\.?\d*\.?\d*)/)
    return {
      name: 'Opera',
      version: match ? match[1] : 'Unknown'
    }
  }
  
  return {
    name: 'Unknown',
    version: 'Unknown'
  }
}

// 解析操作系统信息
function parseOS(userAgent: string, platform: string): { name: string; version: string } {
  const ua = userAgent.toLowerCase()
  
  // Windows
  if (ua.includes('windows')) {
    if (ua.includes('windows nt 10.0')) return { name: 'Windows', version: '10' }
    if (ua.includes('windows nt 6.3')) return { name: 'Windows', version: '8.1' }
    if (ua.includes('windows nt 6.2')) return { name: 'Windows', version: '8' }
    if (ua.includes('windows nt 6.1')) return { name: 'Windows', version: '7' }
    if (ua.includes('windows nt 6.0')) return { name: 'Windows', version: 'Vista' }
    return { name: 'Windows', version: 'Unknown' }
  }
  
  // macOS
  if (ua.includes('mac os x')) {
    const match = ua.match(/mac os x (\d+[._]\d+[._]?\d*)/)
    const version = match ? match[1].replace(/_/g, '.') : 'Unknown'
    return { name: 'macOS', version }
  }
  
  // iOS
  if (ua.includes('iphone') || ua.includes('ipad') || ua.includes('ipod')) {
    const match = ua.match(/os (\d+_\d+_?\d*)/)
    const version = match ? match[1].replace(/_/g, '.') : 'Unknown'
    return { name: 'iOS', version }
  }
  
  // Android
  if (ua.includes('android')) {
    const match = ua.match(/android (\d+\.?\d*\.?\d*)/)
    return {
      name: 'Android',
      version: match ? match[1] : 'Unknown'
    }
  }
  
  // Linux
  if (ua.includes('linux') || platform.toLowerCase().includes('linux')) {
    return { name: 'Linux', version: 'Unknown' }
  }
  
  return { name: 'Unknown', version: 'Unknown' }
}

// 解析设备类型
function parseDeviceType(userAgent: string): 'desktop' | 'mobile' | 'tablet' {
  const ua = userAgent.toLowerCase()
  
  // 平板设备
  if (ua.includes('ipad') || 
      (ua.includes('android') && !ua.includes('mobile')) ||
      ua.includes('tablet')) {
    return 'tablet'
  }
  
  // 移动设备
  if (ua.includes('mobile') || 
      ua.includes('iphone') || 
      ua.includes('ipod') ||
      (ua.includes('android') && ua.includes('mobile'))) {
    return 'mobile'
  }
  
  // 默认为桌面设备
  return 'desktop'
}

// 生成设备名称
function generateDeviceName(
  browser: { name: string; version: string },
  os: { name: string; version: string },
  deviceType: 'desktop' | 'mobile' | 'tablet'
): string {
  const browserName = `${browser.name} ${browser.version.split('.')[0]}`
  const osName = os.version !== 'Unknown' ? `${os.name} ${os.version}` : os.name
  
  const deviceTypeMap = {
    desktop: '桌面设备',
    mobile: '移动设备',
    tablet: '平板设备'
  }
  
  return `${browserName} - ${osName} (${deviceTypeMap[deviceType]})`
}

// 获取地理位置信息（需要用户授权）
export async function getLocationInfo(): Promise<string | null> {
  return new Promise((resolve) => {
    if (!navigator.geolocation) {
      resolve(null)
      return
    }
    
    navigator.geolocation.getCurrentPosition(
      async (position) => {
        try {
          // 这里可以调用地理编码API来获取具体地址
          // 为了简化，我们只返回坐标信息
          const { latitude, longitude } = position.coords
          resolve(`${latitude.toFixed(2)}, ${longitude.toFixed(2)}`)
        } catch (error) {
          resolve(null)
        }
      },
      () => {
        resolve(null)
      },
      {
        timeout: 5000,
        enableHighAccuracy: false
      }
    )
  })
}

// 检测是否为可疑设备
export function isSuspiciousDevice(): boolean {
  const userAgent = navigator.userAgent
  
  // 检测一些常见的爬虫或自动化工具
  const suspiciousPatterns = [
    /bot/i,
    /crawler/i,
    /spider/i,
    /scraper/i,
    /headless/i,
    /phantom/i,
    /selenium/i,
    /webdriver/i
  ]
  
  return suspiciousPatterns.some(pattern => pattern.test(userAgent))
}

// 获取设备指纹（简化版）
export function getDeviceFingerprint(): string {
  const canvas = document.createElement('canvas')
  const ctx = canvas.getContext('2d')
  
  if (ctx) {
    ctx.textBaseline = 'top'
    ctx.font = '14px Arial'
    ctx.fillText('Device fingerprint', 2, 2)
  }
  
  const fingerprint = [
    navigator.userAgent,
    navigator.language,
    screen.width + 'x' + screen.height,
    new Date().getTimezoneOffset(),
    canvas.toDataURL()
  ].join('|')
  
  // 简单的哈希函数
  let hash = 0
  for (let i = 0; i < fingerprint.length; i++) {
    const char = fingerprint.charCodeAt(i)
    hash = ((hash << 5) - hash) + char
    hash = hash & hash // 转换为32位整数
  }
  
  return Math.abs(hash).toString(16)
}
